<?xml version="1.0" encoding="UTF-8"?>
<Bucket
   uuid = "B28A8628-E383-46B5-8378-FE5406A49ACA"
   type = "0"
   version = "2.0">
   <Breakpoints>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "02C17351-504F-4F13-8845-8CF088EDB129"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/Payment/Payer.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "234"
            endingLineNumber = "234"
            landmarkName = "doPayWithSavedCard()"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "0D1EE379-503A-4A1C-B1DE-E279199FEE88"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/Payment/Payer.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "258"
            endingLineNumber = "258"
            landmarkName = "payWithSbp()"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "0AC00DA8-7472-46ED-8B18-34ACF3F633AB"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/Payment/Payer.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "274"
            endingLineNumber = "274"
            landmarkName = "getPaymentInfo(externalService:completion:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "5BBB47E9-89B8-4F03-BF5B-BC4435EFB70E"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/Payment/Payer.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "282"
            endingLineNumber = "282"
            landmarkName = "getPaymentInfo(externalService:completion:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "487DA25E-80A0-4672-AFE2-FE6127E87284"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/Payment/Payer.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "425"
            endingLineNumber = "425"
            landmarkName = "paymentAuthorizationViewController(_:didAuthorizePayment:completion:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "B3B44891-A422-4708-9718-2FBD8DE83766"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/Payment/Payer.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "433"
            endingLineNumber = "433"
            landmarkName = "paymentAuthorizationViewController(_:didAuthorizePayment:completion:)"
            landmarkType = "7">
            <Locations>
               <Location
                  uuid = "B3B44891-A422-4708-9718-2FBD8DE83766 - dcfa9f2cd3e19e45"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "closure #1 () -&gt; () in closure #1 () -&gt; () in closure #1 (Swift.Bool, Swift.Bool) -&gt; () in closure #1 (Swift.Optional&lt;GrowFood_Mobile_App.PaymentInfoResponse&gt;) -&gt; () in GrowFood_Mobile_App.Payer.paymentAuthorizationViewController(_: __C.PKPaymentAuthorizationViewController, didAuthorizePayment: __C.PKPayment, completion: (__C.PKPaymentAuthorizationStatus) -&gt; ()) -&gt; ()"
                  moduleName = "GrowFood Mobile App.debug.dylib"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Projects/gf_ios_app/GrowFood%20Mobile%20App/GrowFood%20Mobile%20App/Classes/Presentation/Payment/Payer.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "433"
                  endingLineNumber = "433">
               </Location>
               <Location
                  uuid = "B3B44891-A422-4708-9718-2FBD8DE83766 - 5264d5d61b93064b"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "closure #1 (Swift.Array&lt;Swift.Array&lt;GrowFood_Mobile_App.ProfileItem&gt;&gt;) -&gt; () in closure #1 () -&gt; () in closure #1 () -&gt; () in closure #1 (Swift.Bool, Swift.Bool) -&gt; () in closure #1 (Swift.Optional&lt;GrowFood_Mobile_App.PaymentInfoResponse&gt;) -&gt; () in GrowFood_Mobile_App.Payer.paymentAuthorizationViewController(_: __C.PKPaymentAuthorizationViewController, didAuthorizePayment: __C.PKPayment, completion: (__C.PKPaymentAuthorizationStatus) -&gt; ()) -&gt; ()"
                  moduleName = "GrowFood Mobile App.debug.dylib"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Projects/gf_ios_app/GrowFood%20Mobile%20App/GrowFood%20Mobile%20App/Classes/Presentation/Payment/Payer.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "433"
                  endingLineNumber = "433">
               </Location>
            </Locations>
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "22C151F2-6400-430C-9690-16D2F313527C"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/Payment/Payer.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "494"
            endingLineNumber = "494"
            landmarkName = "startCheckStatusFor(chargeResponse:isNewCard:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "6F543A5B-CF7C-4267-872F-A9E8A80B00E3"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/Payment/Payer.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "543"
            endingLineNumber = "543"
            landmarkName = "checkSbpStatus(chargeResponse:isNewCard:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "D4A8251F-8706-422B-A8F2-94C08CB000DD"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/Payment/Payer.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "517"
            endingLineNumber = "517"
            landmarkName = "checkSbpStatus(chargeResponse:isNewCard:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "5EED2628-73A7-417A-841C-9F1BCE64F5A8"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/Payment/Payer.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "522"
            endingLineNumber = "522"
            landmarkName = "checkSbpStatus(chargeResponse:isNewCard:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "FF98EAB4-BF85-4EE8-A731-5AE543095ABC"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/Payment/Payer.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "528"
            endingLineNumber = "528"
            landmarkName = "checkSbpStatus(chargeResponse:isNewCard:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "615DEE51-7475-4917-BD21-C1E9A207800C"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/Payment/Payer.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "545"
            endingLineNumber = "545"
            landmarkName = "checkSbpStatus(chargeResponse:isNewCard:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "6CFD11B1-2022-4BAB-81B2-A353D6167E71"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/Payment/Payer.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "592"
            endingLineNumber = "592"
            landmarkName = "checkSbpStatus(chargeResponse:isNewCard:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "AA322488-6847-4511-8ADB-1A73C8978EBC"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/Payment/Payer.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "598"
            endingLineNumber = "598"
            landmarkName = "checkSbpStatus(chargeResponse:isNewCard:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "5104185C-9C9E-4D01-AA01-44FF19B9CEBC"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/NewOrderModules/NewOrder/NewOrder/View/NewOrderViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "66"
            endingLineNumber = "66"
            landmarkName = "viewWillDisappear(_:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "EEDBAB27-C6C3-44C8-939C-F786379CC941"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/NewOrderModules/NewOrder/NewOrder/Presenter/NewOrderPresenter.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "273"
            endingLineNumber = "273"
            landmarkName = "setNewOrderViewModel(_:needToLoadMealDays:forceReload:saveCurrentDate:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "67D87099-7797-40DB-9835-8249360AFE5E"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/NewOrderModules/NewOrder/NewOrder/Presenter/NewOrderPresenter.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "285"
            endingLineNumber = "285"
            landmarkName = "setNewOrderViewModel(_:needToLoadMealDays:forceReload:saveCurrentDate:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "B5C568E4-3670-431C-AD02-9B918B6AFF97"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/BusinessLogic/Services/NewOrder/CustomMenuService/CustomMenuService.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "67"
            endingLineNumber = "67"
            landmarkName = "taskForMenuGettingWith(requestData:showCustom:baseMenu:isCustomEnabled:customLevel:percentDiscount:isLongation:onlyAutoCustom:applyUserCustom:ignoreMealsFilter:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "435A10D3-DB54-46AD-A1C7-5BE512DC08F1"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/BusinessLogic/Services/NewOrder/CustomMenuService/CustomMenuService.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "76"
            endingLineNumber = "76"
            landmarkName = "taskForMenuGettingWith(requestData:showCustom:baseMenu:isCustomEnabled:customLevel:percentDiscount:isLongation:onlyAutoCustom:applyUserCustom:ignoreMealsFilter:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "E04DDBE0-7A70-4820-9A34-0FC7AB17CFB8"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/BusinessLogic/Services/NewOrder/CustomMenuService/CustomMenuService.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "110"
            endingLineNumber = "110"
            landmarkName = "taskForMenuGettingWith(requestData:showCustom:baseMenu:isCustomEnabled:customLevel:percentDiscount:isLongation:onlyAutoCustom:applyUserCustom:ignoreMealsFilter:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "C7909C5C-45C7-4900-832F-879AA88CEF9C"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/BusinessLogic/Services/Menu/MenuService.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "97"
            endingLineNumber = "97"
            landmarkName = "MenuService"
            landmarkType = "3">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "8F75C76B-B382-45EF-B4C7-F453654FE57B"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/NewOrderModules/NewOrder/Custom Flow/NewOrderCustomMenuManual/Interactor/NewOrderCustomMenuManualInteractor.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "31"
            endingLineNumber = "31"
            landmarkName = "init(newOrderViewModel:baseDeliveryData:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "21AC135A-ED61-40C0-A708-BDCD2341EC8E"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/NewOrderModules/NewOrder/Custom Flow/NewOrderCustomMenuManual/Interactor/NewOrderCustomMenuManualInteractor.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "36"
            endingLineNumber = "36"
            landmarkName = "loadMenu(isFirst:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "E7D51915-5E98-499B-9F7C-9213BFBB968F"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/BusinessLogic/Services/NewOrder/CustomMenuService/CustomMenuService.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "322"
            endingLineNumber = "322"
            landmarkName = "loadNewOrderMenuWith(requestData:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "A2CAD145-85C1-4B8A-A88C-4CFFCCA5A70B"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/BusinessLogic/Services/NewOrder/NewOrderMenuService.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "247"
            endingLineNumber = "247"
            landmarkName = "loadNewOrderMenuWith(requestData:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "CE8A6577-7260-4A57-A891-635D4930D6BE"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/BusinessLogic/Services/NewOrder/CustomMenuService/CustomMenuService.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "26"
            endingLineNumber = "26"
            landmarkName = "getMenuViewModel(requestData:isLongation:isCustomEnabled:showCustom:customLevel:percentDiscount:baseMenu:ignoreMealsFilter:applyUserCustom:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "99C75710-3F9F-4A42-9B0D-2585EF4C13F4"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/NewOrderModules/NewOrder/NewOrderMenuTiles/Interactor/NewOrderMenuTilesInteractor.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "47"
            endingLineNumber = "47"
            landmarkName = "loadMenu(with:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "BF6904C3-DF99-4E84-93B6-F1D82C3C95F1"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/NewOrderModules/NewOrder/NewOrderMenuTiles/Presenter/NewOrderMenuTilesPresenter.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "199"
            endingLineNumber = "199"
            landmarkName = "getStringForDatesArray(dates:)"
            landmarkType = "9">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "E70C0229-90DF-4A3F-A6BD-905DD8AB2987"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/NewOrderModules/NewOrder/NewOrderMenuTiles/Presenter/NewOrderMenuTilesPresenter.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "195"
            endingLineNumber = "195"
            landmarkName = "getStringForDatesArray(dates:)"
            landmarkType = "9">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "05DA9934-EACF-49E0-B266-2E38880CD0B5"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/NewOrderModules/NewOrder/NewOrderMenuTiles/Interactor/NewOrderMenuTilesInteractor.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "129"
            endingLineNumber = "129"
            landmarkName = "packRemove(newOrderModel:mealDate:mealNumber:packId:packIndex:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "AE1F8DF0-4B6B-4A82-88F2-DA251D2E9E4D"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Application/BaseRouter.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "123"
            endingLineNumber = "123"
            landmarkName = "showRateApp()"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "776C2C8C-7CB6-47A8-990F-618C02232C38"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/Home/View/HomeViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "685"
            endingLineNumber = "685">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "C4DE8075-AFBA-4C92-BA30-966B9B83380F"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/Home/View/HomeViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "672"
            endingLineNumber = "672">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "89914DB1-52EE-4B2A-93E5-47CFD608BD10"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/BusinessLogic/Services/Orders/ViewModels/OrderDetailViewModel.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "236"
            endingLineNumber = "236"
            landmarkName = "initDeliveries(_:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "D1B46FF1-59A5-4834-9DE6-825C19CC9F86"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/MenuFilter/MenuReplacements/View/CollectionViewSupport/MenuReplacementsCell/MenuReplacementsCell.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "143"
            endingLineNumber = "143"
            landmarkName = "setupButton(changesType:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "E8CADFE3-EB29-4B45-9E71-6DA4E4CDC5C3"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/Orders/OrdersList/Router/OrdersListRouter.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "505"
            endingLineNumber = "505"
            landmarkName = "openAutoLongation(autoLongationBlock:delegate:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "50C4943C-3730-43C3-99FE-4879D199E142"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/Popovers/AutoLongate/AutoLongation/View/AutoLongationViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "66"
            endingLineNumber = "66"
            landmarkName = "setupUI(_:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "6B538BE4-371D-4613-8D75-503AAA12A427"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/BusinessLogic/Services/Orders/OrderService.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "867"
            endingLineNumber = "867"
            landmarkName = "updateOrderAfterDeliveryTransfer(_:fromLongate:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "ED514EFB-18DB-4088-B827-A586B35DF163"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/IMT/IMTFlowContainerModule/View/FlowViews/IMTFlowActivityView/IMTFlowActivityViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "112"
            endingLineNumber = "112"
            landmarkName = "tappedReload(fromMenu:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "94C29084-E46D-4134-95A3-A19A461B0725"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/MenuFilter/FiltersAuth/View/FiltersAuthViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "149"
            endingLineNumber = "149"
            landmarkName = "tappedReload(fromMenu:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "38DAD3D8-8127-4071-943F-DB31A7C73835"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/MenuFilter/FiltersAuthV2/View/FiltersAuthV2ViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "182"
            endingLineNumber = "182"
            landmarkName = "tappedReload(fromMenu:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "D3661CFE-528F-4765-84FE-84E11BA93506"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/MenuFilter/MenuFilterSettings/View/MenuFilterSettingsViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "213"
            endingLineNumber = "213"
            landmarkName = "tappedReload(fromMenu:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "99046474-05F0-46BA-8A10-EB3A19B87344"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/MenuFilter/MenuFilterSettingsV2/View/MenuFilterSettingsV2ViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "266"
            endingLineNumber = "266"
            landmarkName = "tappedReload(fromMenu:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "99FD77FB-8B05-4B8D-82AE-4F229E2EC2AB"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/ProfileModules/DeliveryTemplate/View/DeliveryTemplateViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "223"
            endingLineNumber = "223"
            landmarkName = "tappedReload(fromMenu:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "88D6F3A2-C3E6-4A9E-8288-D71D45AFBC8C"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/ProfileModules/TemplateAddress/View/TemplateAddressViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "98"
            endingLineNumber = "98"
            landmarkName = "tappedReload(fromMenu:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "3849E519-E07F-4FDA-A810-C1BDF8E516BE"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/ProfileModules/FamilyRelationsModule/FamilyRelationsModuleView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "106"
            endingLineNumber = "106"
            landmarkName = "tappedReload(fromMenu:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "A70F8B03-120E-4EFC-B8FC-56616555FE34"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/ProfileModules/FamilyProfileModule/FamilyProfileModulePresenter.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "60"
            endingLineNumber = "60"
            landmarkName = "tappedReload()"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "95066C6B-D8E0-437B-84DF-2B4ECBBAD331"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/ProfileModules/FamilyProfileModule/FamilyProfileModuleView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "243"
            endingLineNumber = "243"
            landmarkName = "tappedReload(fromMenu:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "A6D0A90D-9CC3-42A7-9BB4-992A1D5289A1"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/Orders/OrderMenu/Presenter/OrderMenuPresenter.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "33"
            endingLineNumber = "33"
            landmarkName = "tappedReload(fromMenu:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "1C23A1AD-14C3-4C92-AF19-217463DC51EF"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/Orders/AllDeliveries/View/AllDeliveriesViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "242"
            endingLineNumber = "242"
            landmarkName = "tappedReload(fromMenu:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "C67F386A-2072-46AF-B653-B1F5EEFFF2E4"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/Orders/Longate/IMTLongation/View/IMTLongationViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "152"
            endingLineNumber = "152"
            landmarkName = "tappedReload(fromMenu:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "090D00F5-91FB-4B32-9B4B-15879DD148FF"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/Orders/OrderDetail/DeliveryInfo/DeliveryInfo.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "74"
            endingLineNumber = "74"
            landmarkName = "tappedReload(fromMenu:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "5C1AD7DA-E5CD-4C2F-9A6F-AF89A7EFDA2B"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/Orders/OrderDetail/LoadingOrdersView/LoadingOrdersView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "74"
            endingLineNumber = "74"
            landmarkName = "tappedReload(fromMenu:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "ADDB9289-6A9E-4A5F-A735-B1651435E549"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/Auth/ReferralGift/View/ReferralGiftViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "136"
            endingLineNumber = "136"
            landmarkName = "tappedReload(fromMenu:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "3B946EDD-FB6B-4854-ACC8-FE9519CE37F6"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/NewOrderModules/NewOrder/NewOrderMenuTiles/View/NewOrderMenuTilesViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "543"
            endingLineNumber = "543"
            landmarkName = "createChangesHeaderView(collectionView:indexPath:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "F1DC0F1E-5704-4023-AAE6-7622060A2B09"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/Home/View/Subviews/StoriesView/WidgetStoriesView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "89"
            endingLineNumber = "89"
            landmarkName = "showStoryDeeplink(with:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "BA6DC904-116A-4181-ADE1-1ACA3A1D1AF5"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Common/Analytics/GFAnalytics.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "151"
            endingLineNumber = "151"
            landmarkName = "application(_:didReceiveRemoteNotification:fetchCompletionHandler:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "*************-40FE-A53A-CF1553F83B8D"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Common/Analytics/GFAnalytics.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "135"
            endingLineNumber = "135"
            landmarkName = "application(_:didReceiveRemoteNotification:fetchCompletionHandler:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "1F9D4AFD-63AB-495A-889D-F45C5E923D21"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Common/Analytics/GFAnalytics.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "212"
            endingLineNumber = "212"
            landmarkName = "updateUser()"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "7060FA93-7CD0-4A3D-94CA-38029EA51684"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/Home/Presenter/HomePresenter.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "95"
            endingLineNumber = "95"
            landmarkName = "newOrderDidTap()"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "EEE2A146-1A39-4F46-9BD6-A0BB1DF33BB4"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/Home/Presenter/HomePresenter.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "97"
            endingLineNumber = "97"
            landmarkName = "newOrderDidTap()"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "8EF61787-9871-4E73-A60F-3019B9F89D81"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/ProfileModules/PromoWebView/PromoWebViewModuleView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "85"
            endingLineNumber = "85"
            landmarkName = "webView(_:decidePolicyFor:decisionHandler:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "D80719AF-8C61-4218-AD16-7AD5DF3F56A9"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/BusinessLogic/Network/Responses/Orders/Checkout/GetOrderCheckoutResponse.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "57"
            endingLineNumber = "57"
            landmarkName = "mapping(map:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "E27D1914-4B99-4CDE-AF88-37C0D33FB64A"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/Auth/PromoFreeOnboarding/View/PromoFreeOnboardingViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "148"
            endingLineNumber = "148"
            landmarkName = "continueButtonAction(_:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "B69F6AB6-AA3C-4408-862C-793844B062FB"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/Auth/PromoFreeOnboarding/View/PromoFreeOnboardingViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "151"
            endingLineNumber = "151"
            landmarkName = "continueButtonAction(_:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "D2CF4B03-3FFE-4BC3-A703-1DC7AFB06F3B"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/Orders/NewOrderDetail/CustomViews/PaymentView/View/PaymentView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "168"
            endingLineNumber = "168"
            landmarkName = "changeEnableAutolongate()"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "0E20B7E0-0B7E-4635-B5B8-77F999B28E61"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/Orders/NewOrderDetail/Interactor/NewOrderDetailInteractor.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "196"
            endingLineNumber = "196"
            landmarkName = "setDrinks(isOn:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "1A1AF260-184D-4AE6-BC52-88B87DB91F79"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/Orders/NewOrderDetail/Interactor/NewOrderDetailInteractor.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "66"
            endingLineNumber = "66"
            landmarkName = "createOrderWithOrderData()"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "09E78D56-AF07-4A6C-8E45-DD9C6B420B9B"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/Auth/WelcomeVideoModule/Presenter/WelcomeVideoModulePresenter.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "37"
            endingLineNumber = "37"
            landmarkName = "vkAuthTapped()"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "DF6A025C-819B-4A08-AB59-689B5064979B"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/Auth/WelcomeVideoModule/Interactor/WelcomeVideoModuleInteractor.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "29"
            endingLineNumber = "29"
            landmarkName = "authWithSocialData(_:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "A8EB0184-69D9-448B-ACD2-FBEBEAB7F22B"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/Orders/NewOrderDetail/View/NewOrderDetailViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "445"
            endingLineNumber = "445"
            landmarkName = "NewOrderDetailViewController"
            landmarkType = "21">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "3139DF63-A57A-4243-BAD3-3EBC9050A2BD"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/Orders/NewOrderDetail/View/NewOrderDetailViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "353"
            endingLineNumber = "353"
            landmarkName = "NewOrderDetailViewController"
            landmarkType = "21">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "336B2650-579F-4B7D-9E97-5851CEED6C89"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/Orders/NewOrderDetail/CustomViews/PaymentView/View/PaymentView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "746"
            endingLineNumber = "746"
            landmarkName = "collectionView(_:didSelectItemAt:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "6AE75438-2131-4A41-A5D6-8D38D1C44183"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/Orders/NewOrderDetail/CustomViews/Delivery/View/DeliveryView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "324"
            endingLineNumber = "324"
            landmarkName = "didSelectedAddress(_:forDeliveryDayWithId:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "F0CF2079-73CA-454E-8489-1B4F2A352247"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/Orders/NewOrderDetail/CustomViews/PaymentView/View/PaymentView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "542"
            endingLineNumber = "542"
            landmarkName = "setupDiscountsListViews()"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "64BF4E41-EC16-4025-8732-D0563390DDF4"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/BusinessLogic/Services/Orders/OrderService.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "1118"
            endingLineNumber = "1118"
            landmarkName = "loadCheckoutData(orderIdH:weightLoss:weightUntil:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "6EA58021-A882-4A3E-AA68-E071AB19D483"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/ProfileModules/WalletModule/WalletSettings/View/WalletSettingsViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "93"
            endingLineNumber = "93"
            landmarkName = "paymentFromWalletSwitchValueChanged(_:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "BD7E0518-BFE8-4F52-A758-CF19990F2F31"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/ProfileModules/WalletModule/WalletSettings/View/WalletSettingsViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "140"
            endingLineNumber = "140"
            landmarkName = "WalletSettingsViewController"
            landmarkType = "3">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "3070814C-17FE-48B1-B265-************"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/ProfileModules/WalletModule/WalletSettings/View/WalletSettingsViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "101"
            endingLineNumber = "101"
            landmarkName = "paymentForSubscriptionsSwitchValueChanged(_:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "E65F28B1-8062-4438-A95F-44C0799B6041"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/ProfileModules/WalletModule/WalletSettings/View/WalletSettingsViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "119"
            endingLineNumber = "119"
            landmarkName = "tipsButtonAction(_:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "69B71D22-E041-406F-BD84-0AD50F37B6CA"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/ProfileModules/WalletModule/WalletSettings/View/WalletSettingsViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "149"
            endingLineNumber = "149"
            landmarkName = "updateAutoBonusesSettings(settings:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "EB4C31B3-55C2-48AA-AC1E-B36579D92C06"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/ProfileModules/WalletModule/WalletMain/Wallet/Presenter/WalletPresenter.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "50"
            endingLineNumber = "50"
            landmarkName = "openWalletSettingsModule()"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "85155C14-6341-4DFC-8614-93017EFF2516"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/Orders/NewOrderDetail/CustomViews/Cashback/CashbackDetailsView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "70"
            endingLineNumber = "70"
            landmarkName = "setupUI(_:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "C3F0C6E0-4D14-4C43-BF65-3F33975A1307"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/BusinessLogic/Services/MenuCatalog/MenuCatalogService.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "317"
            endingLineNumber = "317"
            landmarkName = "getMenuTypes(cityId:forceReload:isMenuTypeSelecting:restrictedMenuTypes:useClientsBmi:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "4F7B9860-D03C-4EF2-9C22-C615800C700F"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/BusinessLogic/Services/MenuCatalog/MenuCatalogService.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "330"
            endingLineNumber = "330"
            landmarkName = "getMenuTypes(cityId:forceReload:isMenuTypeSelecting:restrictedMenuTypes:useClientsBmi:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "4B417808-E08C-41BD-B436-CA017F6AC57E"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/NewOrderModules/NewOrder/NewOrder/View/NewOrderViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "46"
            endingLineNumber = "46"
            landmarkName = "viewDidLoad()"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "0127F710-EF05-44F9-B72C-84E25E6CE7B7"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/NewOrderModules/TariffsCatalog/Views/TariffsCatalogBanners/TariffsCatalogBannersView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "25"
            endingLineNumber = "25"
            landmarkName = "banners"
            landmarkType = "24">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "EF0A5A87-32A1-4C06-BA29-2BA9A0010B03"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/NewOrderModules/TariffsCatalog/Views/TariffsCatalogBanners/TariffsCatalogBannersView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "50"
            endingLineNumber = "50"
            landmarkName = "setupView()"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "D417001D-125B-4364-A130-260EB0B13B3A"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/NewOrderModules/TariffsCatalog/Views/TariffsCatalogCards/TariffsCatalogCardsView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "81"
            endingLineNumber = "81"
            landmarkName = "setupCollectionView()"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "E8556F38-C61F-4E3A-A152-46C7BEAB13C4"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/NewOrderModules/TariffsCatalog/MenuTypesCatalog/Router/MenuTypesCatalogRouter.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "51"
            endingLineNumber = "51"
            landmarkName = "openBmi(bmiViewModel:cardViewModel:delegate:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "879307D5-6366-414F-8800-ACE4D8F6B4A5"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/NewOrderModules/TariffsCatalog/BMIMotiovationMenuTypes/View/BMIMotiovationMenuTypesViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "76"
            endingLineNumber = "76"
            landmarkName = "skipBMI()"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "E095BA07-85BA-486A-8FD2-8BE62D9CD5DE"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/NewOrderModules/TariffsCatalog/Views/IMTSummaryNibView/IMTSummaryNibView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "232"
            endingLineNumber = "232"
            landmarkName = "groupItemViewTapped(_:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "6338F830-2910-414D-8B1F-002F48F20A1F"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/NewOrderModules/TariffsCatalog/MenuTypesCatalog/View/MenuTypesCatalogViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "480"
            endingLineNumber = "480"
            landmarkName = "openEditWeight()"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "32CBE706-EF3C-4990-9A10-2138DB7C4456"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/NewOrderModules/TariffsCatalog/MenuTypesCatalog/View/MenuTypesCatalogViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "492"
            endingLineNumber = "492"
            landmarkName = "openAllFilters(delegate:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "C6F6B58E-503C-433C-A02A-C7A1EAF4EFCA"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/NewOrderModules/TariffsCatalog/Views/IMTSummaryNibView/IMTSummaryNibView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "294"
            endingLineNumber = "294"
            landmarkName = "openAllFilters(delegate:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "EAB3F52A-3C38-4E13-8E74-F0115B76C3AF"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/NewOrderModules/TariffsCatalog/Views/IMTSummaryNibView/IMTSummaryNibView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "271"
            endingLineNumber = "271"
            landmarkName = "openMenuType(menuType:firstMealDate:firstMealSession:sectionId:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "8FF7BD6C-A04A-4406-9A87-10B3B070DFF3"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/NewOrderModules/TariffsCatalog/Views/IMTSummaryNibView/IMTSummaryNibView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "278"
            endingLineNumber = "278"
            landmarkName = "openInfoBmi(viewModel:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "48749E06-9A7D-4A67-B956-F01CD326D823"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/NewOrderModules/TariffsCatalog/Views/IMTSummaryNibView/IMTSummaryNibView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "282"
            endingLineNumber = "282"
            landmarkName = "openEditWeight()"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "B9E11483-AA00-437B-B9A2-FC50D2B1ABED"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/NewOrderModules/TariffsCatalog/Views/IMTSummaryNibView/IMTSummaryNibView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "286"
            endingLineNumber = "286"
            landmarkName = "openNormCalories()"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "5498EA6C-FD03-4DDA-AB67-78ADB3B9F360"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/NewOrderModules/TariffsCatalog/Views/IMTSummaryNibView/IMTSummaryNibView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "290"
            endingLineNumber = "290"
            landmarkName = "openEditBMI()"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "D047C0BA-4892-4FE8-9518-0F2C41BF5008"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/NewOrderModules/TariffsCatalog/Views/IMTSummaryNibView/IMTSummaryNibView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "301"
            endingLineNumber = "301"
            landmarkName = "selectedAt(indexPath:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "AE8B2222-72AF-4BAA-8826-AFF921D95A01"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/NewOrderModules/TariffsCatalog/Views/IMTSummaryNibView/IMTSummaryNibView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "305"
            endingLineNumber = "305"
            landmarkName = "tappedInfoAt(indexPath:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "F85843C7-78DA-4BCC-94CC-AACA78A6703D"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/NewOrderModules/TariffsCatalog/Views/IMTSummaryNibView/IMTSummaryNibViewPresenter.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "47"
            endingLineNumber = "47"
            landmarkName = "openGoalAt(indexPath:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "D4E3D6D6-8944-4D6B-A538-879F0BF068AA"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/NewOrderModules/TariffsCatalog/Views/IMTSummaryNibView/IMTSummaryNibView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "280"
            endingLineNumber = "280"
            landmarkName = "openEditWeight()"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "E8E04DEF-2221-484B-BDC1-78A6274104F2"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/Presentation/NewOrderModules/TariffsCatalog/Views/MenuTypesList/CollectionView/MenuTypeCardCollectionViewCell.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "38"
            endingLineNumber = "38"
            landmarkName = "setViewModel(_:forceGrayColor:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "8A332C2A-9F3D-4467-867D-FBF1B7CAC6D1"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "GrowFood Mobile App/Classes/BusinessLogic/Services/MenuCatalog/MenuCatalogViewModels/MenuTypeCardViewModel.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "76"
            endingLineNumber = "76"
            landmarkName = "init(menuType:section:group:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
   </Breakpoints>
</Bucket>
