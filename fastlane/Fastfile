changelog_str = "RC 6.26 "

def info_changelog?(str)
    (str.scan(/(?<=changelog:).*/))
end

def number_after_word_build?(str)
    (str.scan(/#build\s(?!changelog:)\K\S+/))
end

def number_after_word_release?(str)
    (str.scan(/#release\s(?!changelog:)\K\S+/))
end

def is_release(str)
    str.include? "#release"
end

def send(str)
    telegram(
        token: ENV['TG_BOT_TOKEN'],
        chat_id: ENV['TG_CHAT_ID'],
        text: str
    )
end

default_platform(:ios)

platform :ios do
    desc "Push a new beta build to TestFlight"

    lane :build do
        live_version_number = nil
        live_build_number = nil
        File.foreach("../../../version").with_index do |line, line_num|
            if line_num == 0 
                live_version_number = line.delete("\n")
            elsif line_num == 1
                live_build_number = line.delete("\n")
            end
        end
        commit_version = number_after_word_build?("#{ENV['CI_COMMIT_MESSAGE']}")
        commit_version_float = live_version_number

        if !(commit_version.first.nil?)
          commit_version_float = commit_version.first
          live_build_number = 0
        end

	changelog = info_changelog?("#{ENV['CI_COMMIT_MESSAGE']}").first || changelog_str

        send("🚀 Начинаем билд #{commit_version_float}(#{live_build_number.to_i+1})" +
            "\nChangelog: #{changelog}\nАвтор #{ENV['CI_COMMIT_AUTHOR']}"+
            "\nPipeline https://gitlab.gftech.ru/growfood/gf_ios_app/-/pipelines/#{ENV['CI_PIPELINE_ID']}")
        
        increment_version_number(
            version_number: commit_version_float
        )
        increment_build_number(
            build_number: live_build_number.to_i + 1
        )
        clear_derived_data
        build_app(workspace: "GrowFood Mobile App.xcworkspace", 
            scheme: "GrowFood Mobile App", 
            xcargs: "-allowProvisioningUpdates", 
            export_options:{ manageAppVersionAndBuildNumber: false })
    end

	
    lane :test do
        live_version_number = nil
        live_build_number = nil
        File.foreach("../../../version").with_index do |line, line_num|
            if line_num == 0 
                live_version_number = line.delete("\n")
            elsif line_num == 1
                live_build_number = line.delete("\n")
            end
        end
        commit_version = number_after_word_build?("#{ENV['CI_COMMIT_MESSAGE']}")
        commit_version_float = live_version_number

        if !(commit_version.first.nil?)
          commit_version_float = commit_version.first
          live_build_number = 0
        end

	changelog = info_changelog?("#{ENV['CI_COMMIT_MESSAGE']}").first || changelog_str

        send("🚀 Начинаем тестовый билд #{commit_version_float}(#{live_build_number.to_i+1})" +
            "\nChangelog: #{changelog}\nАвтор #{ENV['CI_COMMIT_AUTHOR']}"+
            "\nPipeline https://gitlab.gftech.ru/growfood/gf_ios_app/-/pipelines/#{ENV['CI_PIPELINE_ID']}")
        
        increment_version_number(
            version_number: commit_version_float
        )
        increment_build_number(
            build_number: live_build_number.to_i + 1
        )
        clear_derived_data
	build_app(workspace: "GrowFood Mobile App.xcworkspace", 
		scheme: "GrowFood Mobile App", 
		xcargs: "SYMROOT='/tmp/snapshot'",
		skip_package_ipa: true,
	        skip_archive: true,
		export_options:{ manageAppVersionAndBuildNumber: false },
    	    	destination: "generic/platform=iOS Simulator")
    end

    lane :localbuild do
        build_app(workspace: "GrowFood Mobile App.xcworkspace", 
            scheme: "GrowFood Mobile App", 
            xcargs: "-allowProvisioningUpdates", 
            export_options:{ manageAppVersionAndBuildNumber: false })
    end

    lane :release do
    
        live_version_number = nil
        live_build_number = nil
        File.foreach("../../../version").with_index do |line, line_num|
            if line_num == 0 
                live_version_number = line.delete("\n")
            elsif line_num == 1
                live_build_number = line.delete("\n")
            end
        end
        commit_version = number_after_word_release?("#{ENV['CI_COMMIT_MESSAGE']}")
        commit_version_float = live_version_number

        if !(commit_version.first.nil?)
            commit_version_float = commit_version.first
            live_build_number = 0
        end

	changelog = info_changelog?("#{ENV['CI_COMMIT_MESSAGE']}").first || changelog_str

        send("🚀 Начинаем релизный билд #{commit_version_float}(#{live_build_number.to_i+1})" +
            "\nChangelog: #{changelog}\nАвтор #{ENV['CI_COMMIT_AUTHOR']}"+
            "\nPipeline https://gitlab.gftech.ru/growfood/gf_ios_app/-/pipelines/#{ENV['CI_PIPELINE_ID']}")
        
        increment_version_number(
            version_number: commit_version_float
        )
        increment_build_number(
            build_number: live_build_number.to_i + 1
        )

        build_app(workspace: "GrowFood Mobile App.xcworkspace", 
            scheme: "GrowFood Mobile App CiRelease", 
            xcargs: "-UseModernBuildSystem=NO -allowProvisioningUpdates", 
            export_options:{ manageAppVersionAndBuildNumber: false })
    end

    lane :upload do
        live_version_number = nil
        live_build_number = nil
        File.foreach("../../../version").with_index do |line, line_num|
            if line_num == 0 
                live_version_number = line.delete("\n")
            elsif line_num == 1
                live_build_number = line.delete("\n")
            end
        end
        commit_version = number_after_word_build?("#{ENV['CI_COMMIT_MESSAGE']}")
        commit_version_float = live_version_number

        if !(commit_version.first.nil?)
          commit_version_float = commit_version.first
          live_build_number = 0
        end

	changelog = info_changelog?("#{ENV['CI_COMMIT_MESSAGE']}").first || changelog_str

        send("Пошла заливка #{commit_version_float}(#{live_build_number.to_i+1})\nchangelog: #{changelog}")
        api_key = app_store_connect_api_key(
            key_id: "F5NC6584XH",
            issuer_id: "69a6de95-b413-47e3-e053-5b8c7c11a4d1",
**********************************************************************************************************************************************************************************************************************************************************************************************************************
        )
        upload_to_testflight(
            api_key: api_key,
            changelog: changelog,
            skip_waiting_for_build_processing: true
        )
        File.write("../../../version", "#{commit_version_float}\n#{live_build_number.to_i + 1}")
        if is_release("#{ENV['CI_COMMIT_MESSAGE']}")
            send("✅✅✅ Релизный билд залит #{commit_version_float}(#{live_build_number.to_i+1})\nchangelog: #{changelog}")
        else
            send("✅✅✅ Билд залит #{commit_version_float}(#{live_build_number.to_i+1})\nchangelog: #{changelog}")
        end
    end
  
    error do |lane, exception, options|
        authorTag = "@Medyannik_Dmitry"
        if ENV['CI_COMMIT_AUTHOR'].include? "Kudrin" 
            authorTag = "@kd_kd_kd_kd"
        end
        send("❌❌❌Ошибка при #{lane}\n#{ENV['CI_COMMIT_AUTHOR']} #{authorTag}\nException: #{exception}\nСмотри тут: https://gitlab.gftech.ru/growfood/gf_ios_app/-/pipelines/#{ENV['CI_PIPELINE_ID']}")
    end
end
