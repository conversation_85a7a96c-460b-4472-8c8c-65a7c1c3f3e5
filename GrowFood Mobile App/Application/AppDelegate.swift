//
//  AppDelegate.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 06/08/2018.
//  Copyright © 2018 GrowFood. All rights reserved.
//

import Firebase
import SDWebImage
import SDWebImageSVGCoder
import Cloudpayments
import Mindbox
#if !CI_RELEASE
import TouchVisualizer
import Atlantis
#endif

@UIApplicationMain
final class AppDelegate: BroadcastableAppDelegate {

    static var shared: AppDelegate!
    
    var window: UIWindow?
    private var cachedOptions: [UIApplication.LaunchOptionsKey: Any]? = nil
    private(set) var router: BaseRouter!
    
    override func createServices() -> [UIApplicationDelegate] {
        
        let pushNotificationService = PushNotificationService()
        UNUserNotificationCenter.current().delegate = pushNotificationService
        return [
            RealmAppDelegate(),
            pushNotificationService,
            MindboxAppDelegate(),
            YandexMapKitAppDelegate(),
            GFAnalytics.shared,
            DataDogAppDelegate(),
            DeeplinkAppDelegate()
        ]
    }

    override func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> Bool {
        if GFUserDefaults.shared.needOnboardings {
            GFKeychainService.shared.deleteValue(for: .authToken)
        }
        cachedOptions = launchOptions
        
        AppDelegate.shared = self
        
        self.router = BaseRouter(window: window)
        if let optionsDictionary = launchOptions?[.remoteNotification] as? [String: Any],
           let deferredUrlString = optionsDictionary["url"] as? String, let deferredUrl = URL(string: deferredUrlString) {
            router.deferredUrl = deferredUrl
        }
        router.showAnimatedSplashScreen()
        
        setupWormholy()
        Mindbox.shared.registerBGTasks()
        if let remoteNotification = launchOptions?[.remoteNotification] as? [AnyHashable : Any]
            {
            self.application(application, didReceiveRemoteNotification: remoteNotification) { _ in
                print("notification")
            }
            }
        
        setupAtlantis()
        
        return true
    }

    func initSetup() {

        setupEnvironment()
        GFURLRouter.shared.registerDeeplinkHandlers()
        _ = super.application(UIApplication.shared, didFinishLaunchingWithOptions: cachedOptions)
        setupImageCache()
        setupNavigationBar()
        setTouchVisualizer()
        
        UserService.shared.getCities()
        dishesNumberFormatter.minimumIntegerDigits = 1
        GFUserDefaults.shared.tutorialShowedCalendarBMIDisabled = false
        GFUserDefaults.shared.tutorialShowedFiltersDisabled = false
        GFUserDefaults.shared.tutorialShowedFirstDeliveryMoved = false
        fixCP()
    }
    
    func fixCP() {
         _ = Card.makeCardCryptogramPacket("****************",
                                                             expDate: "11/25",
                                                             cvv: "123",
                                                             merchantPublicID: "pk_068e6d4bfedfe81c21613fee9aadd")
    }
    
    
    override func application(_ application: UIApplication, continue userActivity: NSUserActivity, restorationHandler: @escaping ([UIUserActivityRestoring]?) -> Void) -> Bool {
        _ = super.application(application, continue: userActivity, restorationHandler: restorationHandler)
        
        return true
    }

    override func application(_ app: UIApplication, open url: URL, options: [UIApplication.OpenURLOptionsKey : Any] = [:]) -> Bool {
        _ = super.application(app, open: url, options: options)
        
        if url.absoluteString.contains("vk52802421") {
            VKIDService.shared.open(url: url)
        }
        return true
    }
}

extension AppDelegate {
    private func setupImageCache() {
        let SVGCoder = SDImageSVGCoder.shared
        SDImageCodersManager.shared.addCoder(SVGCoder)
        let config = SDImageCacheConfig.default
        config.maxDiskSize = 1024 * 1024 * 250 // 250MB
        SDImageCache.shared.config.maxDiskSize = 1024 * 1024 * 250 // 250MB
        //ImageCache.shared.removeAll()
        //ImageCache.shared.costLimit = 1024 * 1024 * 250 // 250MB
        //ImageCache.shared.countLimit = 500
    }
    
    private func setupEnvironment() {
#if CI_RELEASE
        environment = .prod
        return
#else
        guard !isFromAppstore() else {
            environment = .prod
            return
        }
        
        if let environmentSave = GFUserDefaults.shared.environment,
           let tempEnvironment = Environment(rawValue: environmentSave) {
            environment = tempEnvironment
        } else {
            environment = .foodTeam
        }
#endif
    }

    private func setupNavigationBar() {
        UINavigationBar.appearance().barTintColor = ColorStyle.GreenBrand.main.value
        UINavigationBar.appearance().backgroundColor = ColorStyle.GreenBrand.main.value
        UINavigationBar.appearance().tintColor = .white
        UINavigationBar.appearance().titleTextAttributes = [.foregroundColor: UIColor.white]
        let navBarAppearance = UINavigationBarAppearance()
        navBarAppearance.configureWithOpaqueBackground()
        navBarAppearance.backgroundColor = ColorStyle.GreenBrand.main.value
        navBarAppearance.shadowColor = .clear
        navBarAppearance.titleTextAttributes = [.foregroundColor: UIColor.white]
        UINavigationBar.appearance().standardAppearance = navBarAppearance
        UINavigationBar.appearance().scrollEdgeAppearance = navBarAppearance
        
        UINavigationBar.appearance(whenContainedInInstancesOf: [GFNavController.self]).barTintColor = ColorStyle.GreenBrand.main.value
        UINavigationBar.appearance(whenContainedInInstancesOf: [GFNavController.self]).backgroundColor = ColorStyle.GreenBrand.main.value
        UINavigationBar.appearance(whenContainedInInstancesOf: [GFNavController.self]).tintColor = .white
        UINavigationBar.appearance(whenContainedInInstancesOf: [GFNavController.self]).titleTextAttributes = [.foregroundColor: UIColor.white]
    }
    
    private func setTouchVisualizer() {
#if !CI_RELEASE
        if GFUserDefaults.shared.isShowTouchVisualizer {
            Visualizer.start()
        }
#endif
    }
}

//MARK: - Установка нетворк логера
extension AppDelegate {
    
    private func setupWormholy() {
#if !CI_RELEASE
        if isFromAppstore() {
            Wormholy.isEnable = false
            return
        }
        
        Wormholy.applicationDidFinishLaunching()
        Wormholy.ignoredUrl = "/chat"
        Wormholy.ignoredHosts = ["facebook.com", "googleapis.com", "storyly.io", "exponea.com", "yandex.net", "crashlytics.com", "github.com", "appcenter.ms", "realm.io", "mongodb-api.com", "app-measurement.com", "cdn.growfood.pro", "storage-review.growfood.pro", "mobile-collector.eu01.nr-data.net", "browser-intake-datadoghq.eu", "api.mindbox.ru", "api.cloudpayments.ru", "certs.tinkoff.ru"]
#endif
    }
    
    private func setupAtlantis() {
#if !CI_RELEASE
        Atlantis.start(hostName: "macbook-pro-16--medyannik.local.")
#endif
    }
    
}
