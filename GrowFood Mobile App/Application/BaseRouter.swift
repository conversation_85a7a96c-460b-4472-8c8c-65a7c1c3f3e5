//
//  BaseRouter.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 22.07.2020.
//  Copyright © 2020 GrowFood. All rights reserved.
//

import StoreKit
import SwiftMessages

final class BaseRouter {
    
    private let window: UIWindow
    var deferredUrl: URL? = nil
    
    init(window: UIWindow?) {
        guard let window = window else {
            self.window = UIWindow(frame: UIScreen.main.bounds)
            return
        }
        
        self.window = window
    }
    
    func showAnimatedSplashScreen() {
        let splashScreen = LaunchAnimationViewController.loadVCFromNib()
        
        window.rootViewController = splashScreen
        window.makeKeyAndVisible()
        let lagFreeField: UITextField = UITextField()
        window.addSubview(lagFreeField)
        lagFreeField.becomeFirstResponder()
        lagFreeField.resignFirstResponder()
        lagFreeField.removeFromSuperview()
    }
    
    func start() {
        if GFUserDefaults.shared.isAuthTinkoff {
            GFUserDefaults.shared.needOnboardings = false
            UserService.shared.logout()
            showLoginScreen()
            return
        }
        
        if GFUserDefaults.shared.needOnboardings {
            GFAnalytics.log(.install(params: [:]))
            GFUserDefaults.shared.needOnboardings = false
            
            showWelcomeVideoScreen()
            GFKeychainService.shared.deleteValue(for: .authToken)
            return
        }
        
        if (!GFUserDefaults.shared.authWasSkipped && !UserService.shared.isAuthorized) || (!UserService.shared.isAuthorized && GFUserDefaults.shared.refBonusPromocode != nil) {
            showWelcomeVideoScreen()
            return
        }
        
        if GFUserDefaults.shared.needWelcomeScreen && (!GFUserDefaults.shared.authWasSkipped || GFUserDefaults.shared.refBonusPromocode != nil) {
            if let promocode = GFUserDefaults.shared.refBonusPromocode, !GFUserDefaults.shared.refBonusCharged {
                GFUserDefaults.shared.needWelcomeScreen = false
                showRefferGiftScreen(promocode)
            } else {
                if UserService.shared.isAuthorized && UserService.shared.curUser != nil {
                    showMainScreen()
                }
                else {
                    UserService.shared.logout()
                }
            }
        } else {
            showMainScreen()
        }
    }
    
    private func showRefferGiftScreen(_ promocode: String) {
        Async.main {
            let type = ReferralBonusType.chargeRefferal(promocode: promocode)
            let referralGiftView = ReferralGiftConfigurator.configure(type: type)
            self.window.rootViewController = referralGiftView
            self.window.makeKeyAndVisible()
            self.animateTransition()
        }
    }
    
    func showLoginScreen() {
        Async.main {
            let loginModuleView = LoginModuleWireframe(isFromStart: true).moduleView
            let loginModuleNavigationController = AuthNavigationController(rootViewController: loginModuleView)
            self.window.rootViewController = loginModuleNavigationController
            self.window.makeKeyAndVisible()
            self.animateTransition()
        }
    }
    
    func showWelcomeVideoScreen() {
        Async.main {
            let welcomeVideoModule = WelcomeVideoModuleConfigurator.configure().navigationController
            self.window.rootViewController = welcomeVideoModule
            self.window.makeKeyAndVisible()
            self.animateTransition()
        }
    }

    func showMainScreen() {
        Async.main {
            let tabBarModule = RootTabBarConfigurator.configure().navigationController
            
            self.window.rootViewController = tabBarModule
            self.window.makeKeyAndVisible()
            self.animateTransition()
            Async.main(after: 0.3) {
                if let deferredUrl = self.deferredUrl {
                    GFURLRouter.shared.openURL(deferredUrl)
                    self.deferredUrl = nil
                }
            }
        }
    }

    func showRateApp() {
        guard !GFUserDefaults.shared.sendedAppRate else { return }
        
        let rateAppView: RateAppView = try! SwiftMessages.viewFromNib()
        let messages = SwiftMessages()
        
        rateAppView.acceptAction = {
            messages.hide()    
            let connectedScenes = UIApplication.shared.connectedScenes
            guard let scene =  connectedScenes.first(where: { $0.activationState == .foregroundActive }) as? UIWindowScene else { return }
            SKStoreReviewController.requestReview(in: scene)
            GFUserDefaults.shared.sendedAppRate = true
        }
        
        rateAppView.noActionTapped = {
            messages.hide()
            GFUserDefaults.shared.sendedAppRate = true
            GFUserDefaults.shared.dateNotAcceptAppRate = Date()
            
            GFURLRouter.shared.newRootTabBar?.presentDriverTips(nil,
                                                                selectedRating: nil)
        }
        
        rateAppView.closeActionTapped = {
            messages.hide()
        }
        
        rateAppView.backView.alpha = 1

        var config = messages.defaultConfig
        config.presentationContext = .window(windowLevel: UIWindow.Level.statusBar)
        config.duration = .forever
        config.preferredStatusBarStyle = .lightContent
        config.interactiveHide = false
        config.presentationStyle = .center
        config.dimMode = .color(color: UIColor.black.alpha(0.5), interactive: true)
        messages.show(config: config, view: rateAppView)
    }
    
    func openUrl(_ url: URL) {
        Async.main {
            GFURLRouter.shared.openURL(url)
        }
    }
    
    func openLink(_ link: String) {
        guard let url = URL(string: link) else { return }
        openUrl(url)
    }
    
    func animateTransition() {
        UIView.transition(with: window,
                              duration: 0.3,
                              options: .transitionCrossDissolve,
                              animations: nil,
                              completion: nil)
    }
}
