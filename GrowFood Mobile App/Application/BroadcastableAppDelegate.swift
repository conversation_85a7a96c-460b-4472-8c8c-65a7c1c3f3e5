//
//  BroadcastableAppDelegate.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 22.07.2020.
//  Copyright © 2020 GrowFood. All rights reserved.
//

class BroadcastableAppDelegate: NSObject, UIApplicationDelegate {
    
    private lazy var services: [UIApplicationDelegate] = createServices()
    
//    Need to overload from superclass
    func createServices() -> [UIApplicationDelegate] {
        return []
    }
    
    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey : Any]? = nil) -> Bool {
        services.forEach { _ = $0.application?(application, didFinishLaunchingWithOptions: launchOptions) }
        return true
    }
    
    func application(_ app: UIApplication, open url: URL, options: [UIApplication.OpenURLOptionsKey : Any] = [:]) -> Bool {
        services.forEach { _ = $0.application?(app, open: url, options: options) }
        return true
    }
    
    func application(_ application: UIApplication, continue userActivity: NSUserActivity, restorationHandler: @escaping ([UIUserActivityRestoring]?) -> Void) -> Bool {
        services.forEach { _ = $0.application?(application, continue: userActivity, restorationHandler: restorationHandler) }
        return true
    }
    
    func applicationDidBecomeActive(_ application: UIApplication) {
        services.forEach { $0.applicationDidBecomeActive?(application) }
    }
    
    func application(_ application: UIApplication, didRegisterForRemoteNotificationsWithDeviceToken deviceToken: Data) {
        services.forEach { $0.application?(application, didRegisterForRemoteNotificationsWithDeviceToken: deviceToken) }
    }
    
    func application(_ application: UIApplication, didReceiveRemoteNotification userInfo: [AnyHashable : Any], fetchCompletionHandler completionHandler: @escaping (UIBackgroundFetchResult) -> Void) {
        services.forEach { $0.application?(application, didReceiveRemoteNotification: userInfo, fetchCompletionHandler: completionHandler) }
    }
}
