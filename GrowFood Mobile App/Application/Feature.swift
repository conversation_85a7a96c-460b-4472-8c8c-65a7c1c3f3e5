//
//  Feature.swift
//  GrowFood Mobile App
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 19.10.2023.
//  Copyright © 2023 GrowFood. All rights reserved.
//

import Foundation

/// Сущность всех фичи флагов.
/// isUsingRemoteConfig - уставнавливается в логере, устанавиливает возможность изменить флаг локально или через удаленный конфиг
public class Feature: NSObject {
    
    enum Key: String, CaseIterable {
        case isAddressInRegistrationEnabled
        case isCustomizationAllowed
        case isDraftOrderFlowEnabled
        
        var title: String {
            switch self {
            case .isAddressInRegistrationEnabled:
                return "Флоу регистрации"
            case .isCustomizationAllowed:
                return "Разрешена ли кастомизация"
            case .isDraftOrderF<PERSON>Enabled:
                return "Корзина заказа"
            }
        }
        
        var descr: String? {
            switch self {
            case .isAddressInRegistrationEnabled:
                return "С видео"
            case .isCustomizationAllowed:
                return "Признак используется для запрета первой кастомизации"
            case .isDraftOrderFlowEnabled:
                return "Заказ отображается в списке активных после оплаты"
            }
        }
    }
    
    static func setRemoteFlag(with type: Feature.Key, isOn: Bool) {
        guard GFUserDefaults.shared.isUsingRemoteConfig else { return }
        UserDefaults.standard.set(isOn, forKey: type.rawValue)
    }

    static func setLocalFlag(with type: Feature.Key, isOn: Bool) {
        guard !GFUserDefaults.shared.isUsingRemoteConfig else { return }
        UserDefaults.standard.set(isOn, forKey: type.rawValue)
    }
    
    static func isOn(_ type: Feature.Key, orDefault: Bool? = true) -> Bool {
        let def = orDefault ?? true
        return UserDefaults.standard.value(forKey: type.rawValue) as? Bool ?? def
    }
    
    static func getAllCases() -> [Feature.Key: Bool] {
        var allCases: [Feature.Key: Bool] = [:]
        let keyCases = Key.allCases.sorted { left, right in
            left.rawValue > right.rawValue
        }
        keyCases.forEach { item in
            allCases[item] = Feature.isOn(item)
        }
        return allCases
    }
}
