//
//  MindboxAppDelegate.swift
//  GrowFood Mobile App
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 25.07.2024.
//  Copyright © 2024 GrowFood. All rights reserved.
//

import Foundation
import Mindbox
import MindboxLogger

final class MindboxAppDelegate: NSObject, UIApplicationDelegate {
    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey : Any]? = nil) -> Bo<PERSON> {
        do {
            
            let mindboxSdkConfig = try MBConfiguration(
                endpoint: "growfood.growfood.IosApp",
                domain: "api.mindbox.ru",
                subscribeCustomerIfCreated: true,
                shouldCreateCustomer: true
            )
            Mindbox.logger.logLevel = .debug
            Mindbox.shared.initialization(configuration: mindboxSdkConfig)
            
            Mindbox.shared.track(.launch(launchOptions))
            
        } catch  {
            print(error)
        }
        return true
    }
    
    func application(_ application: UIApplication, didRegisterForRemoteNotificationsWithDeviceToken deviceToken: Data) {
        Mindbox.shared.apnsTokenUpdate(deviceToken: deviceToken)
    }
}
