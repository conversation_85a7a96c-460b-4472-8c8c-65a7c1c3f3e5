//
//  DatadogAppDelegate.swift
//  GrowFood Mobile App
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 20.08.2024.
//  Copyright © 2024 GrowFood. All rights reserved.
//

import DatadogCore
import DatadogTrace
import DatadogRUM

final class DataDogAppDelegate: NSObject, UIApplicationDelegate {
    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey : Any]? = nil) -> Bool {
        Datadog.verbosityLevel = .debug
        Datadog.initialize(
            with: Datadog.Configuration(
                clientToken: Constants.Datadog.clientToken,
                env: "ios",
                site: .eu1
            ),
            trackingConsent: .granted
        )
        Datadog.verbosityLevel = .debug
        Trace.enable(with: .init(networkInfoEnabled: true))
        
        RUM.enable(
            with: RUM.Configuration(
                applicationID: Constants.Datadog.appId,
                uiKitViewsPredicate: DefaultUIKitRUMViewsPredicate(),
                uiKitActionsPredicate: De<PERSON>ultUIKitRUMActionsPredicate()
            )
        )
        
        return true
    }
}
