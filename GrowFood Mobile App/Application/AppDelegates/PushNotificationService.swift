//
//  PushNotificationManager.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 22.07.2020.
//  Copyright © 2020 GrowFood. All rights reserved.
//

import Mindbox

final class PushNotificationService: NSObject, UIApplicationDelegate {
    func application(_ app: UIApplication, open url: URL, options: [UIApplication.OpenURLOptionsKey : Any] = [:]) -> Bool {
        UNUserNotificationCenter.current().delegate = self
        return true
    }
    
    func application(_ application: UIApplication, didRegisterForRemoteNotificationsWithDeviceToken deviceToken: Data) {
        let token = deviceToken.map { String(format: "%02.2hhx", $0) }.joined()
        GFUserDefaults.shared.pushToken = token
        
    }
}

extension PushNotificationService: UNUserNotificationCenterDelegate {

    func userNotificationCenter(_ center: UNUserNotificationCenter, willPresent notification: UNNotification, withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void) {
        logger.info(notification.request.content)

        var urlString = notification.request.content.userInfo["url"] as? String
        if urlString == nil {
            if let customDict = notification.request.content.userInfo["custom"] as? [String: Any] {
                if let aDict = customDict["a"] as? [String: Any] {
                    urlString = aDict["link"] as? String
                }
            }
        }
        completionHandler([.banner,.sound])
    }

    func userNotificationCenter(_ center: UNUserNotificationCenter, didReceive response: UNNotificationResponse, withCompletionHandler completionHandler: @escaping () -> Void) {
        Mindbox.shared.track(.push(response))
        Mindbox.shared.pushClicked(response: response)
        if let attr = response.notification.request.content.userInfo["attributes"] as? Dictionary<String, Any>, let utm = attr["utm"] as? String {
            if let utmDict = utm.convertToDictionary() {
                GFUserDefaults.shared.utmValue = utmDict
                GFUserDefaults.shared.utmCreationDate = Date()
            }
        }
        else if let attr = response.notification.request.content.userInfo["attributes"] as? Dictionary<String, Any>, let utm = attr["utm"] as? Dictionary<String, Any> {
            GFUserDefaults.shared.utmValue = utm
            GFUserDefaults.shared.utmCreationDate = Date()
        }
        
        if let pushModel = Mindbox.shared.getMindboxPushData(userInfo: response.notification.request.content.userInfo), Mindbox.shared.isMindboxPush(userInfo: response.notification.request.content.userInfo) {
            var urlString = ""
            if let buttons = pushModel.buttons, let clickedButton = buttons.first(where: { $0.uniqueKey == response.actionIdentifier}), let buttonUrl = clickedButton.url  {
                urlString = buttonUrl
            } else if let clickUrl = pushModel.clickUrl {
                urlString = clickUrl
            }
            
            // Обработайте URL клика
            if let url = URL(string: urlString) {
                UIApplication.shared.open(url)
            }
        }
    }
}
