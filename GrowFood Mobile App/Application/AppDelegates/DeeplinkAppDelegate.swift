//
//  DeeplinkAppDelegate.swift
//  GrowFood Mobile App
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 11.05.2023.
//  Copyright © 2023 GrowFood. All rights reserved.
//

import Foundation
import FirebaseDynamicLinks
import SafariServices
import Mindbox

final class DeeplinkAppDelegate: NSObject, UIApplicationDelegate {
    
    func application(_ app: UIApplication, open url: URL, options: [UIApplication.OpenURLOptionsKey : Any] = [:]) -> <PERSON><PERSON> {
        
        /// FirebaseDynamicLinks
        if let dynamicUrl = DynamicLinks.dynamicLinks().dynamicLink(fromCustomSchemeURL: url)?.url {
            if let promocode = dynamicUrl.queryItems?.first(where: { $0.name == "invite" })?.value {
                GFUserDefaults.shared.refBonusPromocode = promocode
            }
            
            /// Переход на описание акций
            if dynamicUrl.absoluteString.contains("marketing_activity"),
               let deeplink = URL(string: "gf://marketingActivity") {
                GFURLRouter.shared.openURL(deeplink, fromContinueUserActivity: true)
            }
            
            /// Начать авторизацию через Тинькофф
            if let auth = dynamicUrl.queryItems?.first(where: { $0.name == "type" })?.value, auth == "tinkoff" {
                GFUserDefaults.shared.isAuthTinkoff = !UserService.shared.tinkoffOfferFlowPassed
                AppDelegate.shared.router.start()
            }
        }
        
        /// Успешная авторизация Тинькофф
        if url.absoluteString.contains("tinkoffauthorized") {
            return TinkoffService.shared.tinkoffId.handleCallbackUrl(url)
        }
        
        if url.absoluteString.contains("gf:") || url.absoluteString.contains("growfood.pro") {
            
            if let _ = url.queryItems?.first(where: { $0.name == "invite" })?.value,
               !UserService.shared.isAuthorized {
                AppReadiness.runNowOrWhenAppWillLaunchReady {
                    GFURLRouter.shared.openURL(url, fromContinueUserActivity: true)
                }
            } 
            //Диплинк авторизации(Только в дебаге)
            else if url.absoluteString.contains("authorization") {
                AppReadiness.runNowOrWhenAppWillLaunchReady {
                    GFURLRouter.shared.openURL(url, fromContinueUserActivity: true)
                }
            }
            else {
                AppReadiness.runNowOrWhenAppWillBecomeReady {
                    
                    /// Кейс когда открыт сафари
                    if GFURLRouter.shared.safariVC != nil {
                        GFURLRouter.shared.safariVC?.dismiss(animated: true)
                    }
                    
                    GFURLRouter.shared.openURL(url, fromContinueUserActivity: true)
                }
            }
        }
        
        return true
    }
    
    
    func application(_ application: UIApplication, continue userActivity: NSUserActivity, restorationHandler: @escaping ([UIUserActivityRestoring]?) -> Void) -> Bool {
        guard let url = userActivity.webpageURL else { return false }
        Mindbox.shared.track(.universalLink(userActivity))
        ///FirebaseDynamicLinks
        let _ = DynamicLinks.dynamicLinks().handleUniversalLink(url) { (dynamicLink, error) in
            if let promocode = dynamicLink?.url?.queryItems?.first(where: { $0.name == "invite" })?.value  {
                GFUserDefaults.shared.refBonusPromocode = promocode
            }
            
            /// Начать авторизацию через Тинькофф
            if let auth = dynamicLink?.url?.queryItems?.first(where: { $0.name == "type" })?.value, auth == "tinkoff" {
                GFUserDefaults.shared.isAuthTinkoff = !UserService.shared.tinkoffOfferFlowPassed
                AppDelegate.shared.router.start()
            }
        }
        
        guard userActivity.activityType == NSUserActivityTypeBrowsingWeb else { return false }
        
        if url.absoluteString.contains("gf:") || url.absoluteString.contains("growfood.pro") {
            if let _ = url.queryItems?.first(where: { $0.name == "invite" })?.value,
               !UserService.shared.isAuthorized {
                AppReadiness.runNowOrWhenAppWillLaunchReady {
                    GFURLRouter.shared.openURL(url, fromContinueUserActivity: true)
                }
            }
            //Диплинк авторизации(Только в дебаге)
            else if url.absoluteString.contains("authorization") {
                AppReadiness.runNowOrWhenAppWillLaunchReady {
                    GFURLRouter.shared.openURL(url, fromContinueUserActivity: true)
                }
            }
            else {
                AppReadiness.runNowOrWhenAppWillBecomeReady {
                    GFURLRouter.shared.openURL(url, fromContinueUserActivity: true)
                }
            }
        }
        
        /// Переход на описание акций
        else if url.absoluteString.contains("marketing_activity") {
            AppReadiness.runNowOrWhenAppWillBecomeReady {
                guard let deeplink = URL(string: "gf://marketingActivity") else { return }
                GFURLRouter.shared.openURL(deeplink, fromContinueUserActivity: true)
            }
        }
        
        /// Начать авторизацию через Тинькофф
        else if url.absoluteString.contains("tinkoffauth") {
            AppReadiness.runNowOrWhenAppWillLaunchReady {
                GFUserDefaults.shared.isAuthTinkoff = !UserService.shared.tinkoffOfferFlowPassed
            }
        }

        return true
    }
}

