//
//  RealmAppDelegate.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 22.07.2020.
//  Copyright © 2020 GrowFood. All rights reserved.
//

import RealmSwift

final class RealmAppDelegate: NSObject, UIApplicationDelegate {
    
    private var schemaVersion: UInt64 {
        #if DEBUG
        if let currentRealmSchemaVersion = GFUserDefaults.shared.currentRealmSchemaVersion {
            GFUserDefaults.shared.currentRealmSchemaVersion = currentRealmSchemaVersion + 1
            return UInt64(currentRealmSchemaVersion + 1)
        } else {
            GFUserDefaults.shared.currentRealmSchemaVersion = 1
            return 1
        }
        #else
        let appVersion = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String
        let shortVersion = appVersion?.replacingOccurrences(of: ".", with: "") ?? "0"
        let finalVersion = shortVersion.count == 3 ? shortVersion + "0" : shortVersion.prefix(4)
        return UInt64(finalVersion) ?? 0
        #endif
    }

    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey : Any]? = nil) -> Bool {
        
        let configuration = Realm.Configuration.init(schemaVersion: schemaVersion,
                                                     migrationBlock: nil)
        Realm.Configuration.defaultConfiguration = configuration
    
        let realm = try! Realm()

        try! realm.write {
            realm.delete(realm.objects(MealDay.self), cascading: true)
            realm.delete(realm.objects(PseudoOrderUserConfig.self), cascading: true)
            realm.delete(realm.objects(OrderMenuUserConfig.self), cascading: true)
            realm.delete(realm.objects(MenuType.self), cascading: true)
            realm.objects(OrderMenu.self).forEach { (orderMenu) in
                orderMenu.shouldReload = true
            }
        }

        realm.refresh()
     
        return true
    }
    
}
