//
//  AppReadiness.swift
//  GrowFood Mobile App
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 11.05.2023.
//  Copyright © 2023 GrowFood. All rights reserved.
//

import Foundation

public class AppReadiness: NSObject {
    
    private static let shared = AppReadiness()
    
    typealias BlockType = () -> Void
    var launchBlock: BlockType?
    var currentBlock: BlockType?
    
    /// Приложение запущено на лаунч экране
    static var isAppLaunch: Bool = false {
        didSet {
            Async.main {
                AppReadiness.shared.launchBlock?()
                AppReadiness.shared.launchBlock = nil
            }
        }
    }
    
    /// Приложение запущено на табБаре
    static var isAppReady: Bool = false {
        didSet {
            Async.main {
                AppReadiness.shared.currentBlock?()
                AppReadiness.shared.currentBlock = nil
            }
        }
    }
    
    static func setAppIsReady() {
        isAppReady = true
    }
    
    static func setAppIsLaunch() {
        isAppLaunch = true
    }
    
    static func runNowOrWhenAppWillLaunchReady(_ block: @escaping BlockType) {
        if isAppLaunch {
            Async.main {
                block()
            }
        } else {
            AppReadiness.shared.launchBlock = block
        }
    }
    
    static func runNowOrWhenAppWillBecomeReady(_ block: @escaping BlockType) {
        if isAppReady {
            Async.main {
                block()
            }
        } else {
            AppReadiness.shared.currentBlock = block
        }
    }
    
}
