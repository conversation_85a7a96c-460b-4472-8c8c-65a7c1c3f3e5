//
//  GetCustomMenuResponse.swift
//  GrowFood Mobile App
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 09.06.2023.
//  Copyright © 2023 GrowFood. All rights reserved.
//

import Foundation
import ObjectMapper
import DatadogTrace

public class GetCustomMenuResponse: Mappable {
    
    public var firstDeliveryDate: Date = Date()
    public var session: String = ""
    public var packs: [String: PackResponse] = [String: PackResponse]()
    public var deliveries: [CustomMenuDeliveryResponse] = [CustomMenuDeliveryResponse]()
    public var properties: [String: [String]] = [String: [String]]()
    public var filteredProperties: [String: [String]] = [String: [String]]()
//    public var properties: [CustomMenuPackProperties] = []
//    public var filteredProperties: [CustomMenuPackProperties] = []

    public required init?(map: ObjectMapper.Map) {
        
    }
    
    public func mapping(map: ObjectMapper.Map) {
        let currentMenuTrace = PerformanceTracker.datadogTraces["newOrderMenuLoading"]
        let parseTrace = Tracer.shared().startSpan(operationName: "parseMenu", childOf: currentMenuTrace?.context)
        packs <- map["data.packs"]
        deliveries <- map["data.deliveries"]
        properties <- map["data.properties"]
        filteredProperties <- map["data.filteredProperties"]
        updatePropertiesInPacks()
        session <- map["data.session"]
        var firstDeliveryDateString = ""
        firstDeliveryDateString <- map["data.firstDeliveryDate"]
        firstDeliveryDate = DateFormatter.yyyyMMdd.date(from: firstDeliveryDateString) ?? Date()
        if session == "morning" {
            firstDeliveryDate = firstDeliveryDate.adjust(.day, offset: -1)
        }
        deliveries.forEach { delivery in
            delivery.days.forEach { (menuDay) in
                menuDay.items.forEach({ (customMenuItem) in
                    let majorPacks = customMenuItem.majorPackIds.compactMap {
                        packs[String($0)]
                    }
                    var tempMajorPacks: [PackResponse] = []
                    var tempSecondaryPacks: [PackResponse] = []
                    majorPacks.forEach({ (pack) in
                        pack.isMajor = true
                        if let packPrice = customMenuItem.prices[String(pack.id)] {
                            pack.price = Double(packPrice)
                        }
                        else {
                            pack.price = pack.basePrice
                        }
                        tempMajorPacks.append(PackResponse(pack: pack))
                    })
                    let secondaryPacks = customMenuItem.secondaryPackIds.compactMap{ packs[String($0)] }
//                    packs.filter({ (pack) -> Bool in
//                        customMenuItem.secondaryPackIds.contains(pack.id)
//                    })
                    secondaryPacks.forEach({ (pack) in
                        pack.isMajor = false
                        
                        if let customPackPrice = customMenuItem.prices[String(pack.id)] {
                            pack.price = Double(customPackPrice)
                        }
                        else {
                            pack.price = pack.basePrice
                        }
                        tempSecondaryPacks.append(PackResponse(pack: pack))
                    })
                    customMenuItem.packs.append(contentsOf: tempMajorPacks)
                    customMenuItem.packs.append(contentsOf: tempSecondaryPacks)
                })
            }
        }
        let parsePropertiesTrace = Tracer.shared().startSpan(operationName: "parseProperties", childOf: parseTrace.context)
        
        parsePropertiesTrace.finish()
        parseTrace.finish()
    }
    
    private func updatePropertiesInPacks() {
        print("start ", #function, " \(currentTimeInMilliSeconds())")
        let allProperties = MenuService.shared.packsProperties
        packs.forEach { pack in
            if let packPropertiesIds = properties[pack.key] {
                var packProperties = [(PackPropertyLabelFormatResponse, String)]()
                packPropertiesIds.forEach { packPropertyId in
                    if let propertyObject = allProperties?[packPropertyId] {
                        if let labelFormat = propertyObject.labelFormat {
                            packProperties.append((labelFormat, propertyObject.name))
                        }
                    }
                }
                pack.value.updateLabelProperties(packProperties)
            }
            if let packFilteredPropertiesIds = filteredProperties[pack.key] {
                var packProperties = [String]()
                packFilteredPropertiesIds.forEach { packPropertyId in
                    if let propertyObject = allProperties?[packPropertyId] {
                        packProperties.append(propertyObject.name)
                    }
                }
                pack.value.updateFilteredProperties(packProperties)
            }
        }
        print("end ", #function, " \(currentTimeInMilliSeconds())")
    }
    
    func makeBaseMenuResponse() {
        filteredProperties = [:]
        clearPacksProperties()
        for delivery in deliveries {
            delivery.customization = delivery.customization.filter({ (item) -> Bool in
                return item.isAutoCustom
            })
        }
    }
    
    func clearPacksProperties() {
        for delivery in deliveries {
            // Сначала проходим по все дням
            for day in delivery.days {
                // В каждом дне проходим по всем приемам
                for item in day.items {
                    // В каждом приеме проходим по всем свойствам
                    for pack in item.packs {
                        pack.filteredProperties = []
                        pack.isFiltered = false
                    }
                }
            }
        }
    }
    
}

public final class CustomMenuDeliveryResponse: Mappable {
    public var deliveryDate: Date = Date()
    public var deliveryIdH: String = ""
    public var days: [CustomMenuDayResponse] = []
    public var customization: [CustomMenuConfigItem] = []
    public var config: CustomMenuDeliveryConfigResponse? = nil
    public var productionTypeId: Int = 0
    
    public init?(map: ObjectMapper.Map) {
        
    }
    
    public func mapping(map: ObjectMapper.Map) {
        let dateFormatter = DateFormatter.yyyyMMdd
        var deliveryDateString = ""
        deliveryIdH <- map["deliveryId_H"]
        deliveryDateString <- map["plannedDeliveryDate"]
        deliveryDate = dateFormatter.date(from: deliveryDateString) ?? Date()
        days <- map["days"]
        customization <- map["customization"]
        config <- map["config"]
        productionTypeId <- (map["productionTypeId"], anyToIntTransform)
    }
}

public class CustomMenuDeliveryConfigResponse: Mappable {
    
    public var numberOfDaysInDelivery: Int = 0
    public var minPacksForFreeDelivery: Int = 1
    public var deliveryPenaltyPrice: Int = 0
    public var minPackCountForPaidRemove: Int = 0
    public var canUseFilters: Bool = false
    public var oneDayDeliveryPrice: Int = 0
    public var packRemovePrice: Int = 0
    public var minPackCountPerDelivery: Int = 0
    public var maxPackCountPerDelivery: Int = 0
    public var canUseClientRestrictions: Bool = false
    
    public required init?(map: ObjectMapper.Map) {
        
    }
    
    public func mapping(map: ObjectMapper.Map) {
        numberOfDaysInDelivery <- (map["numberOfDaysInDelivery"], anyToIntTransform)
        minPacksForFreeDelivery <- (map["minPacksForFreeDelivery"], anyToIntTransform)
        deliveryPenaltyPrice <- (map["deliveryPenaltyPrice"], anyToIntTransform)
        minPackCountForPaidRemove <- (map["minPackCountForPaidRemove"], anyToIntTransform)
        canUseFilters <- (map["canUseFilters"], anyToBoolTransform)
        oneDayDeliveryPrice <- (map["oneDayDeliveryPrice"], anyToIntTransform)
        packRemovePrice <- (map["packRemovePrice"], anyToIntTransform)
        minPackCountPerDelivery <- (map["minPackCountPerDelivery"], anyToIntTransform)
        maxPackCountPerDelivery <- (map["maxPackCountPerDelivery"], anyToIntTransform)
        canUseClientRestrictions <- (map["canUseClientRestrictions"], anyToBoolTransform)
    }
}


public class CustomMenuDayResponse: Mappable {
    
    public var items: [CustomMenuItemResponse] = [CustomMenuItemResponse]()
    public var mealDate: Date = Date()
    public var customizable: Bool = false
    public var mealDateString: String = ""
    
    public required init?(map: ObjectMapper.Map) {
        
    }
    
    public func mapping(map: ObjectMapper.Map) {
        items <- map["items"]
        mealDateString <- map["mealDate"]
        mealDate = DateFormatter.yyyyMMdd.date(from: mealDateString) ?? Date()
        customizable <- (map["customizable"], anyToBoolTransform)
    }
}



