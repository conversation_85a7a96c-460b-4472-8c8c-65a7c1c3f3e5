//
//  GetTariffSecionsResponse.swift
//  GrowFood Mobile App
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 07.11.2022.
//  Copyright © 2022 GrowFood. All rights reserved.
//

import ObjectMapper

final class GetTariffSectionsResponse: Mappable {
    
    var icons: [TariffCategoryIconResponse] = []
    var dropdownSections: [TariffCategorySectionResponse] = []
    var bigSections: [TariffCategorySectionResponse] = []
    var smallSections: [TariffCategorySectionResponse] = []
    
    public required init?(map: ObjectMapper.Map) { }
    
    public func mapping(map: ObjectMapper.Map) {
        icons <- map["icons"]
        dropdownSections <- map["sections.dropdown"]
        bigSections <- map["sections.big"]
        smallSections <- map["sections.small"]
    }
}

final class TariffCategoryIconResponse: Mappable {
    var image: String = ""
    var name: String = ""
    
    public required init?(map: ObjectMapper.Map) { }
    
    public func mapping(map: ObjectMapper.Map) {
        image <- map["image"]
        name <- map["name"]
    }
}

final class TariffCategorySectionResponse: Mappable {
    var action: String = ""
    var deepLinkCode: String = ""
    var link: String? = nil
    var groups: [TariffCategoryGroupResponse] = []
    var id: String = ""
    var title: String = ""
    var subtitle: String = ""
    var cardSettings: TariffCategoryCardSettingsResponse? = nil
    var image: String? = nil
    var isDefault: Bool = false
    var cardTitle: String = ""
    var cardSubtitle: String = ""
    
    public required init?(map: ObjectMapper.Map) { }
    
    public func mapping(map: ObjectMapper.Map) {
        cardTitle <- map["cardTitle"]
        cardSubtitle <- map["cardSubtitle"]
        deepLinkCode <- map["deepLinkCode"]
        isDefault <- (map["isDefault"], anyToBoolTransform)
        subtitle <- map["subtitle"]
        link <- map["link"]
        action <- map["action"]
        groups <- map["groups"]
        if groups.isEmpty {
            var group: TariffCategoryGroupResponse? = nil
            group <- map["groups"]
            if let group {
                groups.append(group)
            }
        }
        id <- map["id"]
        title <- map["title"]
        cardSettings <- map["cardSettings"]
        image <- map["image"]
    }
}

final class TariffCategoryGroupResponse: Mappable {
    var sort: TariffCategorySortResponse? = nil
    var filter: TariffCategoryFilterResponse? = nil
    var cardSettings: TariffCategoryCardSettingsResponse? = nil
    var limit: Int = 0
    var title: String = ""
    var sortOrder: Int = 0
    var descriptionText: String = ""
    
    public required init?(map: ObjectMapper.Map) { }
    
    public func mapping(map: ObjectMapper.Map) {
        descriptionText <- map["description"]
        sort <- map["sort"]
        filter <- map["filter"]
        title <- map["title"]
        cardSettings <- map["cardSettings"]
        limit <- (map["limit"], anyToIntTransform)
        sortOrder <- (map["sortOrder"], anyToIntTransform)
    }
}

final class TariffCategorySortResponse: Mappable {
    var direction: String = ""
    var field: String = ""
    
    public required init?(map: ObjectMapper.Map) { }
    
    public func mapping(map: ObjectMapper.Map) {
        direction <- map["direction"]
        field <- map["field"]
    }
}

final class TariffCategoryFilterResponse: Mappable {
    var stringValue: String? = nil
    var boolValue: Bool? = nil
    var intValue: Int? = nil
    var field: String = ""
    
    public required init?(map: ObjectMapper.Map) { }
    
    public func mapping(map: ObjectMapper.Map) {
        field <- map["field"]
        let value = map.JSON["value"]
        switch value {
        case is Bool:
            boolValue = value as? Bool
            if boolValue == nil {
                boolValue = (value as? NSNumber)?.boolValue
            }
        case is Int:
            intValue = value as? Int
        case is String:
            stringValue = value as? String
        default:
            return
        }
    }
}

final class TariffCategoryCardSettingsResponse: Mappable {
    
    var background: String = ""
    var info: TariffCategoryCardSettingsInfoResponse? = nil
    var title: TariffCategoryCardSettingsTitleResponse? = nil
    var right: TariffCategoryCardSettingsTitleResponse? = nil
    var left: TariffCategoryCardSettingsTitleResponse? = nil
    
    
    public required init?(map: ObjectMapper.Map) { }
    
    public func mapping(map: ObjectMapper.Map) {
        background <- map["background"]
        info <- map["info"]
        title <- map["title"]
        right <- map["right"]
        left <- map["left"]
    }
}

final class TariffCategoryCardSettingsInfoResponse: Mappable {
    var showIcon: Bool = true
    var color: String = ""
    var field: String = ""
    var background: String = ""
    var prefix: String = ""
    var postfix: String = ""
    var postfixChoice: [String] = []
    
    public required init?(map: ObjectMapper.Map) { }
    
    public func mapping(map: ObjectMapper.Map) {
        field <- map["field"]
        showIcon <- map["showIcon"]
        //TODO: - удалить
        showIcon = false
        color <- map["color"]
        background <- map["background"]
        postfix <- map["postfix"]
        postfixChoice <- map["postfix_choice"]
        prefix <- map["prefix"]
    }
}

final class TariffCategoryCardSettingsTitleResponse: Mappable {
    var color: String = ""
    var field: String = ""
    var icon: String = ""
    var prefix: String = ""
    var postfix: String = ""
    var postfixChoice: [String] = []
    
    public required init?(map: ObjectMapper.Map) { }
    
    public func mapping(map: ObjectMapper.Map) {
        prefix <- map["prefix"]
        color <- map["color"]
        field <- map["field"]
        icon <- map["icon"]
        postfix <- map["postfix"]
        postfixChoice <- map["postfix_choice"]
    }
}
