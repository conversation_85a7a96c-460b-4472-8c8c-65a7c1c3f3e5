//
//  GetMenuTypesCatalogResponse.swift
//  GrowFood Mobile App
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 07.11.2022.
//  Copyright © 2022 GrowFood. All rights reserved.
//

import ObjectMapper

final class GetMenuTypesCatalogResponse: Mappable {
    
    var menuTypes: [MenuTypeResponse] = []
    var bmiInfo: GoalBMIInfoResponse? = nil
    
    public required init?(map: ObjectMapper.Map) { }
    
    public func mapping(map: ObjectMapper.Map) {
        menuTypes <- map["menuTypes"]
        bmiInfo <- map["bmiInfo"]
    }
}


class MenuTypeResponse: Mappable {
    
    var menuType: String = ""
    var cityId: Int = 0
    var numberOfMeals: Int = 0
    var title: String = ""
    var id: Int = 0
    var menuId: Int = 0
    var realNumberOfMeals: Int = 0
    var numberOfMealDays: Int = 0
    var defaultNumberOfDays: Int = 0
    var defaultSubscriptionDays: Int? = nil
    var defaultStartDate: Date? = nil
    var defaultStartSession: String = ""
    var defaultStartDateCustom: Date? = nil
    var defaultStartSessionCustom: String = ""
    var price: Int = 0
    var reason: String = ""
    var shortDescription: String = ""
    var shortReason: String = ""
    var priceType: String = ""
    var customizable: Bool = false
    var canUseFilters: Bool = false
    var canUseClientRestrictions: Bool = false
    var canUseFilterCalendar: Bool = false
    var dayFilterTemplateId: Int? = nil
    var kcal: Int = 0
    var nutritionMacros: String = "" /// "Б32 • Ж37 • У66"
    var pricePlanId: Int = 0
    var marks: [GoalMenuTypeMarkResponse] = []
    var intValues: [String: Int] = [:]
    var boolValues: [String: Bool] = [:]
    var stringValues: [String: String] = [:]
    var dailyItems: [String] = []
    var marketingInfo: GoalMarketingInfoResponse? = nil
    var defaultStartTimeFrom: String? = nil
    var defaultStartTimeTo: String? = nil
    var defaultStartTimeFromCustom: String? = nil
    var defaultStartTimeToCustom: String? = nil
    var isMenuTypeAvailableForAddress: Bool = true
    var availableForSubscription: Bool = true
    var showDuplicates: Bool = true
    var bmiInfo: GoalBMIInfoResponse? = nil
    var numberOfMealsString: String = ""
    
    public required init?(map: ObjectMapper.Map) { }
    
    public func mapping(map: ObjectMapper.Map) {
        showDuplicates <- (map["showDuplicates"], anyToBoolTrueTransform)
        availableForSubscription <- (map["availableForSubscription"], anyToBoolTrueTransform)
        shortDescription <- map["shortDescription"]
        numberOfMealsString <- map["numberOfMealsString"]
        priceType <- map["priceType"]
        bmiInfo <- map["bmiInfo"]
        kcal <- (map["kcal"], anyToIntTransform)
        defaultStartTimeFrom <- map["defaultStartTimeFrom"]
        defaultStartTimeTo <- map["defaultStartTimeTo"]
        defaultStartTimeFromCustom <- map["defaultStartTimeFromCustom"]
        defaultStartTimeToCustom <- map["defaultStartTimeToCustom"]
        isMenuTypeAvailableForAddress <- (map["isMenuTypeAvailableForAddress"], anyToBoolTrueTransform)
        let dateFormatter = DateFormatter.yyyyMMdd
        
        menuType <- map["menuType"]
        title <- map["title"]
        numberOfMeals <- (map["numberOfMeals"], anyToIntTransform)
        id <- (map["id"], anyToIntTransform)
        
        realNumberOfMeals <- (map["realNumberOfMeals"], anyToIntTransform)
        numberOfMealDays <- (map["numberOfMealDays"], anyToIntTransform)
        defaultNumberOfDays <- (map["defaultNumberOfDays"], anyToIntTransform)
        defaultSubscriptionDays <- (map["defaultSubscriptionDays"], anyToIntTransform)
        
        var defaultStartDateString: String = ""
        
        defaultStartDateString <- map["defaultStartDate"]
        defaultStartDate = dateFormatter.date(from: defaultStartDateString)
        
        defaultStartSession <- map["defaultStartSession"]
        var defaultStartDateCustomString: String = ""
        defaultStartDateCustomString <- map["defaultStartDateCustom"]
        defaultStartDateCustom = dateFormatter.date(from: defaultStartDateCustomString)
        defaultStartSessionCustom <- map["defaultStartSessionCustom"]
        
        price <- (map["price"], anyToIntTransform)
        reason <- map["reason"]
        dailyItems <- map["libData.dailyItems"]
        marks <- map["pricePlan.libData.marks"]
        shortReason <- map["shortReason"]
        customizable <- (map["customizable"], anyToBoolTransform)
        canUseFilters <- (map["canUseFilters"], anyToBoolTransform)
        canUseClientRestrictions <- (map["canUseClientRestrictions"], anyToBoolTransform)
        canUseFilterCalendar <- (map["canUseFilterCalendar"], anyToBoolTransform)
        dayFilterTemplateId <- (map["dayFilterTemplateId"], anyToIntTransform)
        menuId <- (map["menuId"], anyToIntTransform)
        pricePlanId <- (map["pricePlanId"], anyToIntTransform)
        marketingInfo <- map["marketingInfo"]
        
        for (key, value) in map.JSON {
            switch value {
            case is String:
                stringValues[key] = value as? String
            case is Bool:
                boolValues[key] = value as? Bool
            case is Int:
                intValues[key] = value as? Int
            default:
                break
            }
        }
    }
}

public class GoalBMIInfoResponse: Mappable {
    
    var motivation: GoalBMIMotivationResponse? = nil
    var askTargetParams: Bool = false
    
    public required init?(map: Map) {
        
    }
    
    public func mapping(map: Map) {
        askTargetParams <- (map["askTargetParams"], anyToBoolTransform)
        motivation <- map["motivation"]
    }
}

public class GoalMenuTypeMarkResponse: Mappable {
    private(set) var text: String = ""
    private(set) var textColorHex: String = ""
    private(set) var backgroundColorHex: String = ""
    private(set) var gradientIdentifier: GradientIdentifier? = nil
    
    public required init?(map: Map) {
        
    }
    
    public func mapping(map: Map) {
        text <- map["text"]
        
        textColorHex <- map["text_color"]
        backgroundColorHex <- map["color"]
        
        var gradientIdentifierString = ""
        gradientIdentifierString <- map["gradient_identifier"]
        gradientIdentifier = GradientIdentifier(rawValue: gradientIdentifierString)
    }
}

public class GoalBMIMotivationResponse: Mappable {
    var showSkipButton: Bool = false
    var title: String = ""
    var mainButtonText: String = ""
    var mainButtonAction: String = ""
    var items: [GoalBMIMotivationItemResponse] = []
    
    public required init?(map: Map) {
        self.mapping(map: map)
    }
    
    public func mapping(map: Map) {
        showSkipButton <- (map["showSkipButton"], anyToBoolTransform)
        title <- map["title"]
        mainButtonText <- map["mainButtonText"]
        mainButtonAction <- map["mainButtonAction"]
        items <- map["list"]
    }
}

public class GoalBMIMotivationItemResponse: Mappable {
    var image: String = ""
    var text: String = ""
    
    public required init?(map: Map) {
        self.mapping(map: map)
    }
    
    public func mapping(map: Map) {
        image <- map["image"]
        text <- map["text"]
    }
}

public class GoalMarketingInfoResponse: Mappable {
    var title: String = ""
    var text: String = ""
    var image: String = ""
    
    public required init?(map: Map) {
        self.mapping(map: map)
    }
    
    public func mapping(map: Map) {
        title <- map["title"]
        text <- map["text"]
        image <- map["image"]
    }
}
