//
//  SendSMSResponse.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 21/08/2018.
//  Copyright © 2018 GrowFood. All rights reserved.
//

import Foundation
import ObjectMapper

public struct SendSMSResponse: Mappable {
    public var success: Bool = false
    public var client: SendSMSResponseClient? = nil
    public var resendAfter: Int?
    
    public init?(map: ObjectMapper.Map) {
//        self.mapping(map: map)
    }
    
    public mutating func mapping(map: ObjectMapper.Map) {
        success <- (map["success"], anyToBoolTransform)
        client <- map["profile"]
        resendAfter <- (map["resendAfter"], anyToIntTransform)
    }
}

public struct SendSMSResponseClient: Mappable {
    public var isNew: Bool?
    
    public init?(map: ObjectMapper.Map) {
//        mapping(map: map)
    }
    
    public mutating func mapping(map: ObjectMapper.Map) {
        isNew <- (map["isNew"], anyToBoolTransform)
    }
}
