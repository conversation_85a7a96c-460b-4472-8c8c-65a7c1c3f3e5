//
//  SmsPhoneAuthResponse.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 22/08/2018.
//  Copyright © 2018 GrowFood. All rights reserved.
//

import Foundation
import ObjectMapper

public struct SmsPhoneAuthResponse: Mappable {
    public var success: Bool = false
    public var profile: ProfileResponse? = nil
    public var token: String? = nil
    
    public init?(map: ObjectMapper.Map) {
//        self.mapping(map: map)
    }
    
    public mutating func mapping(map: ObjectMapper.Map) {
        success <- (map["success"], anyToBoolTransform)
        profile <- map["profile"]
        token <- map["token"]
        profile?.authToken = token
    }
}
