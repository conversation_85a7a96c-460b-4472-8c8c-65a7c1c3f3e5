//
//  GetPreauthResponse.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 08.08.2020.
//  Copyright © 2020 GrowFood. All rights reserved.
//

import Foundation
import ObjectMapper

public struct GetPreauthResponse: Mappable {
    
    public var success: Bool = false
    public var showSkipAuthButton: Bool = false // показать ли кнопку пропуска авторизации
    public var canLoginFacebook: Bool = false // показать ли авторизацию через фейсбук
    public var canAppleIdLogin: Bool = false // через apple id
    public var canLoginVK: Bool = false // через vk
    public var canLoginTinkoff: Bool = false // через tinkoff
    
    public init?(map: ObjectMapper.Map) { }
    
    public mutating func mapping(map: ObjectMapper.Map) {
        success <- (map["success"], anyToBoolTransform)
        showSkipAuthButton <- (map["showSkipAuthButton"], anyToBoolTransform)
        canLoginFacebook <- (map["canLoginFacebook"], anyToBoolTransform)
        canLoginVK <- (map["canLoginVK"], anyToBoolTransform)
        canAppleIdLogin <- (map["canAppleIdLogin"], anyToBoolTransform)
        canLoginTinkoff <- (map["canLoginTinkoff"], anyToBoolTransform)
        
        var currentTimestamp: Int64? = nil
        currentTimestamp <- (map["currentTimestamp"], anyToInt64Transform)
        let utcTimestamp = Int64(Date().timeIntervalSince1970 * 1000)
        GFUserDefaults.shared.diffServerTime = nil
        
        if let currentTimestamp, abs(utcTimestamp - currentTimestamp) > (500 * 1000) {
            GFUserDefaults.shared.diffServerTime = utcTimestamp - currentTimestamp
        }
    }
}
