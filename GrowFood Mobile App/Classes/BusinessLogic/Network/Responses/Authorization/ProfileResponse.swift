//
//  AuthResponseClient.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 23/08/2018.
//  Copyright © 2018 GrowFood. All rights reserved.
//

import Foundation
import ObjectMapper

public class ProfileResponse: Mappable {
    public var idH: String?
    public var id: Int = -1
    public var firstName: String?
    public var avatar: String?
    public var lastName: String?
    public var authToken: String?
    public var cityId: Int?
    public var addressesCount: Int = 0

    public var bmi: Int = 0
    public var city: String = ""
    public var phone: String?
    public var email: String?
    public var gender: String?
    public var dob: String? // день рождения
    
    public var activityStatus: String? // new, active, old
    public var canCreateForgottenOrder: Bool = false
    public var canCreateFamilyForgottenOrder: Bool = false
    
    public var needToAddData: Bool?
    public var lastFilterOptions: [FilterOptionResponse] = []
    public var clientTags: [String] = []
    
    public var wasDeletingRequested: Bool = false

    public var bmiFilled: Bool = false
    public var dailyKcalForFastWeightLoss: Int = 0
    public var dailyKcalForLightWeightLoss: Int = 0
    public var dailyKcalForExtremeWeightLoss: Int = 0
    public var dailyKcalNormal: Int = 0
    public var height: Int = 0
    public var weight: Double = 0
    public var purposeWeight: Int = 0
    public var lifestyleIdH: String = ""
    public var menuTypeGroupIdH: String = ""
    public var menuTypeGroupReason: String = ""
    public var menuTypeGroupShortName: String = ""
    
    public var minPurposeWeight: Double = 0
    public var maxPurposeWeight: Double = 0
    public var minIndicationWeight: Double = 0
    public var maxIndicationWeight: Double = 0
    public var normalWeightFrom: Double = 0
    public var normalWeightTo: Double = 0
    public var defaultPurposeWeight: Double = 0
    public var perfectWeight: Double = 0
    
    public var menuCalendarConfig: MenuCalendarConfigResponse?
    public var bonusApplyMaxPercent: Int? /// Максимальный процент который можно оплатить бонусами
    public var bonusApplyMaxPercentMessage: String? /// Замена bonusApplyMaxPercent, тут приходит полностью готовый текст
    public var showWidgetsTab: Bool = false
    public var tinkoffOfferFlowPassed: Bool = false /// Авторизован через Тинькофф по реферальной ссылке. Не через кнопку авторизации
    public var referrerRewardSum: Int? /// Сколько получит Приглашающий
    public var referralRewardSum: Int? /// Cколько получить Приглашенный
    
    public var cityAvailableSessions: [String] = []
    
    //public var loyaltyStatus: LoyaltyStatusResponse?

    required public init?(map: ObjectMapper.Map) {
        //mapping(map: map)
    }
    
    public func mapping(map: ObjectMapper.Map) {
        wasDeletingRequested <- (map["wasDeletingRequested"], anyToBoolTransform)
        minIndicationWeight <- (map["minIndicationWeight"], anyToDoubleTransform)
        maxIndicationWeight <- (map["maxIndicationWeight"], anyToDoubleTransform)
        minPurposeWeight <- (map["minPurposeWeight"], anyToDoubleTransform)
        maxPurposeWeight <- (map["maxPurposeWeight"], anyToDoubleTransform)
        normalWeightFrom <- (map["normalWeightFrom"], anyToDoubleTransform)
        normalWeightTo <- (map["normalWeightTo"], anyToDoubleTransform)
        defaultPurposeWeight <- (map["defaultPurposeWeight"], anyToDoubleTransform)
        perfectWeight <- (map["perfectWeight"], anyToDoubleTransform)
        bmi <- (map["bmi"], anyToIntTransform)
        clientTags <- map["clientTags"]
        height <- (map["height"], anyToIntTransform)
        weight <- (map["weight"], anyToDoubleTransform)
        purposeWeight <- (map["purposeWeight"], anyToIntTransform)
        lifestyleIdH <- map["lifestyle.id_H"]
        menuTypeGroupIdH <- map["menuTypeGroup.id_H"]
        menuTypeGroupReason <- map["menuTypeGroup.reason"]
        menuTypeGroupShortName <- map["menuTypeGroup.shortName"]
        bmiFilled <- (map["bmiFilled"], anyToBoolTransform)
        dailyKcalForFastWeightLoss <- (map["dailyKcalForFastWeightLoss"], anyToIntTransform)
        dailyKcalForLightWeightLoss <- (map["dailyKcalForLightWeightLoss"], anyToIntTransform)
        dailyKcalForExtremeWeightLoss <- (map["dailyKcalForExtremeWeightLoss"], anyToIntTransform)
        dailyKcalNormal <- (map["dailyKcalNormal"], anyToIntTransform)
        idH <- map["id_H"]
        firstName <- map["firstName"]
        avatar <- map["avatar"]
        lastName <- map["lastName"]
        gender <- map["gender"]
        cityId <- (map["cityId"], anyToIntTransform)
        id <- (map["id"], anyToIntTransform)
        addressesCount <- (map["addressesCount"], anyToIntTransform)
        phone <- map["phone"]
        email <- map["email"]
        dob <- map["birthday"]
        activityStatus <- map["activityStatus"]
        needToAddData <- (map["needToAddData"], anyToBoolTransform)
        lastFilterOptions <- map["lastFilterOptions"]
        menuCalendarConfig <- map["menuCalendarConfig"]
        bonusApplyMaxPercent <- map["bonusApplyMaxPercent"]
        bonusApplyMaxPercentMessage <- (map["bonusApplyMaxPercentMessage"], anyToStringTransform)
        showWidgetsTab <- map["config.showWidgetsTab"]
        tinkoffOfferFlowPassed <- (map["tinkoffOfferFlowPassed"], anyToBoolTransform)
        referrerRewardSum <- (map["referralProgram.referrerRewardSum"], anyToIntTransform)
        referralRewardSum <- (map["referralProgram.referralRewardSum"], anyToIntTransform)
        cityAvailableSessions <- map["cityAvailableSessions"]
    }
}

public class SeparatedBalanceResponse: Mappable {
    public var title: String = ""
    public var value: Double = 0
    public var valuePerOrder: Double = 0
    public var type: String = ""
    
    required public init?(map: ObjectMapper.Map) {
    //        mapping(map: map)
        }
        
    public func mapping(map: ObjectMapper.Map) {
        value <- (map["value"], anyToDoubleTransform)
        valuePerOrder <- (map["valuePerOrder"], anyToDoubleTransform)
        title <- map["title"]
        type <- map["type"]
    }
}

public class BonusBalanceResponse: Mappable {

    public var value: Int = 0
    public var text: String = ""
    public var descriptionText: String = ""
    public var bonusUseLimitPercent: Int = 50
    
    required public init?(map: ObjectMapper.Map) {
    //        mapping(map: map)
    }
        
    public func mapping(map: ObjectMapper.Map) {
        value <- (map["value"], anyToIntTransform)
        text <- map["text"]
        descriptionText <- map["description"]
        bonusUseLimitPercent <- (map["bonusUseLimitPercent"], anyToIntTransform)
    }
}

public class AutoBonusesSettingsResponse: Mappable {

    private(set) var order: Bool = false
    private(set) var longation: Bool = false
    
    var stringsArray: [String] {
        var result = [WalletService.AutoBonusesSettingsType]()
        
        if order {
            result.append(.order)
        }
        
        if longation {
            result.append(.longation)
        }
        
        return result.map { $0.rawValue }
    }
    
    static var `default` = AutoBonusesSettingsResponse(order: false, longation: false)
    
    required public init?(map: ObjectMapper.Map) { }
    
    init(order: Bool, longation: Bool) {
        self.order = order
        self.longation = longation
    }
        
    public func mapping(map: ObjectMapper.Map) {
        order <- (map["order"], anyToBoolTransform)
        longation <- (map["longation"], anyToBoolTransform)
    }
}

public struct MenuCalendarConfigResponse: Mappable {
    var globalEnabled = false
    var filter: Filter?
    
    public init?(map: Map) { }
    mutating public func mapping(map: Map) {
        globalEnabled <- map["globalEnabled"]
        filter <- map["filter"]
    }
    
    public struct Filter: Mappable {
        var meals: Int?
        var days: Int?
        
        public init?(map: Map) { }
        mutating public func mapping(map: Map) {
            meals <- map["meals"]
            days <- map["days"]
        }
    }
}
