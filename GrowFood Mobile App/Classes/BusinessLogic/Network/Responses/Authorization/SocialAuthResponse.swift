//
//  SocialAuthResponse.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 23/08/2018.
//  Copyright © 2018 GrowFood. All rights reserved.
//

import Foundation
import ObjectMapper

public struct SocialAuthResponse: Mappable {
    
    public var success: Bool = false
    public var isRegistered: Bool = false
    public var profile: ProfileResponse? = nil
    public var token: String? = nil
    
    public init?(map: ObjectMapper.Map) {
//        self.mapping(map: map)
    }
    
    public mutating func mapping(map: ObjectMapper.Map) {
        print(map.JSON)
        success <- (map["success"], anyToBoolTransform)
        isRegistered <- (map["isRegistered"], anyToBoolTransform)
        profile <- map["profile"]
        token <- map["token"]
        profile?.authToken = token
    }
}
