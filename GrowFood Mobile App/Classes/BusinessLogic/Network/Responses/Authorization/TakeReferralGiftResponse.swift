//
//  TakeReferralGiftResponse.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 24.07.2020.
//  Copyright © 2020 GrowFood. All rights reserved.
//

import ObjectMapper

public class TakeReferralGiftResponse: Mappable {
    
    enum GiftType: String {
        case `default`, loyalty
    }
    
    public var success: Bool = false
    public var bonusCharged: Bool = false
    
    public var clientMessage: String = ""
    public var clientTitle: String = ""
    public var message: String = ""
    public var chargeSum: Double = 0
    
    public var clientNotificationTitle: String = ""
    public var clientNotificationDescription: String = ""
    
    private(set) var type: GiftType = .default
    
    required public init?(map: ObjectMapper.Map) { }
    
    public func mapping(map: ObjectMapper.Map) {
        success <- (map["success"], anyToBoolTransform)
        bonusCharged <- (map["bonusCharged"], anyToBoolTransform)
        
        clientMessage <- map["clientMessage"]
        clientTitle <- map["clientTitle"]
        message <- map["clientNotificationMessage"]
        chargeSum <- (map["chargeSum"], anyToDoubleTransform)
        clientNotificationTitle <- map["clientNotificationTitle"]
        clientNotificationDescription <- map["clientNotificationDescription"]
        
        var typeString = "default"
        typeString <- map["type"]
        type = GiftType(rawValue: typeString) ?? .default
    }
}
