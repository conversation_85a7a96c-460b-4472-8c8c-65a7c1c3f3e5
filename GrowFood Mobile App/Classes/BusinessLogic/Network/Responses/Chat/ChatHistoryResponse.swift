//
//  ChatHistoryResponse.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 11/07/2019.
//  Copyright © 2019 GrowFood. All rights reserved.
//

import Foundation
import ObjectMapper
import DeepDiff

public class ChatHistoryResponse: Mappable {
    
    public var success: Bool = false
    public var messages: [ChatMessageResponse] = [ChatMessageResponse]()
    
    required public init?(map: ObjectMapper.Map) {
    }
    
    public func mapping(map: ObjectMapper.Map) {
        success <- (map["success"], anyToBoolTransform)
        messages <- map["messages"]
    }
    
}

public class ChatMessageResponse: Mappable, DiffAware {
    static func == (lhs: ChatMessageResponse, rhs: ChatMessageResponse) -> Bool {
        return lhs.hashValue == rhs.hashValue
    }
    
    var hashValue: Int {
        return idH.hashValue
    }
    
    public static func compareContent(_ a: ChatMessageResponse, _ b: ChatMessageResponse) -> <PERSON><PERSON> {
        return a.hashValue == b.hashValue
    }
    
    public var diffId: Int {
        return idH.hashValue
    }
    
    public var idH: String = ""
    public var externalId: String = ""
    public var type: String = ""
    public var text: String = ""
    public var date: Date = Date()
    public var attachments: [String] = []
    
    init(id: String,
         text: String) {
        self.idH = id
        self.type = "from_client"
        self.date = Date()
        self.text = text
    }
    
    required public init?(map: ObjectMapper.Map) {
    }
    
    public func mapping(map: ObjectMapper.Map) {
        idH <- map["id_H"]
        type <- map["type"]
        externalId <- map["external_id"]
        if externalId == "" {
            externalId = UUID().uuidString
        }
        text <- map["text"]
        var dateString = ""
        dateString <- map["date"]
        let timeDateFormatter = DateFormatter.empty
        timeDateFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ssxxxxx"
        date = timeDateFormatter.date(from: dateString) ?? Date()
        var dict: [[String: String]] = []
        dict <- map["attachments"]
        dict.forEach { (dictionary) in
            if let link = dictionary["link"] {
                attachments.append(link)
            }
        }
    }
    
}

