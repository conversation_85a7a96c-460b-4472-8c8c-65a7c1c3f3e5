//
//  CreateFamilyMemberResponse.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 25.05.2020.
//  Copyright © 2020 GrowFood. All rights reserved.
//

import Foundation
import ObjectMapper

public class CreateFamilyMemberResponse: Mappable {
    
    public var familyMember: FamilyMemberResponse!
    
    public required init?(map: ObjectMapper.Map) {
        self.mapping(map: map)
    }
    
    public func mapping(map: ObjectMapper.Map) {
        familyMember <- map["familyMember"]
    }
    
}
