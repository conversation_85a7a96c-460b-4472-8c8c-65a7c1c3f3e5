//
//  DeleteFamilyMemberResponse.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 15.06.2020.
//  Copyright © 2020 GrowFood. All rights reserved.
//

import Foundation
import ObjectMapper

public class DeleteFamilyMemberResponse: Mappable {
    
    public var success: Bool = false
    public var deleted: Bool = false
    
    public required init?(map: ObjectMapper.Map) {
        self.mapping(map: map)
    }
    
    public func mapping(map: ObjectMapper.Map) {
        success <- map["success"]
        deleted <- map["deleted"]
    }
    
}

