//
//  GetFamilyDataResponse.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 18.05.2020.
//  Copyright © 2020 GrowFood. All rights reserved.
//

import Foundation
import ObjectMapper

public class GetFamilyDataResponse: Mappable {
    
    public var familyMembers: [FamilyMemberResponse] = [FamilyMemberResponse]()
    public var relations: [FamilyRelationResponse] = [FamilyRelationResponse]()
    
    public required init?(map: ObjectMapper.Map) {
        self.mapping(map: map)
    }
    
    public func mapping(map: ObjectMapper.Map) {
        familyMembers <- map["familyMembers"]
        relations <- map["relations"]
    }
    
}


public class FamilyMemberResponse: Mappable {
    
    public var hasCompletedOrders: Bool = false
    public var idH: String = ""
    public var relation: FamilyRelationResponse!
    
    public required init?(map: ObjectMapper.Map) {
        self.mapping(map: map)
    }
    
    public func mapping(map: ObjectMapper.Map) {
        idH <- map["id_H"]
        relation <- map["clientPhoneRelation"]
        hasCompletedOrders <- (map["hasCompletedOrders"], anyToBoolTransform)
    }
    
}

public class FamilyRelationResponse: Mappable {
    
    public var idH: String = ""
    public var title: String = ""
    public var name: String = ""
    public var genitiveTitle: String = ""
    
    
    public required init?(map: ObjectMapper.Map) {
        self.mapping(map: map)
    }
    
    public func mapping(map: ObjectMapper.Map) {
        idH <- map["id_H"]
        title <- map["title"]
        name <- map["name"]
        genitiveTitle <- map["genitiveTitle"]
    }
    
}
