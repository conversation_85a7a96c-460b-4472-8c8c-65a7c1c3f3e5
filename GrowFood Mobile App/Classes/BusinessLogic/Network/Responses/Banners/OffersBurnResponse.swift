//
//  OffersBurnResponse.swift
//  GrowFood Mobile App
//
//  Created by Medyann<PERSON> on 24.05.2022.
//  Copyright © 2022 GrowFood. All rights reserved.
//

import Foundation
import ObjectMapper

public struct OffersBurnResponse: Mappable {
    
    public var success: Bool = false
    
    public init?(map: ObjectMapper.Map) {
        self.mapping(map: map)
    }
    
    public mutating func mapping(map: ObjectMapper.Map) {
        success <- (map["success"], anyToBoolTransform)
    }
}

