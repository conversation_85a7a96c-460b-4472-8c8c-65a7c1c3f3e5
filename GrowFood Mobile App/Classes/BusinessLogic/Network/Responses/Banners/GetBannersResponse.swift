//
//  GetBannersResponse.swift
//  GrowFood Mobile App
//
//  Created by Medyannik Dmitri on 24.05.2022.
//  Copyright © 2022 GrowFood. All rights reserved.
//

import Foundation
import ObjectMapper
import UIKit

public enum BannerType: String {
    case fullscreenBanner = "fullscreen"                            /// Обычный баннер(Использовался для 100-го кэшбека)
    case fullscreenBurnOfferBanner = "fullscreen_with_timer"        /// Сгорающий оффер
    case fullscreenBlackFridayBanner = "fullscreen_black_friday"    /// Черная пятница
    case fullscreenTinkoffBanner = "tinkoff"                        /// Баннер для Тинькофф
    case burnOfferBanner = "carousel_with_timer"                    /// Сгорающий оффер (оффер действует меншье 24 часов)
    case marketingActivityBanner = "carousel_marketing_activity"    /// Золотая лихорадка (отсчет до конца недели)
    case blackFridayBanner = "carousel_black_friday"                /// Черная пятница (Отсчет количества подарков в одном часу)
    case homeWithClose = "home_with_close"                          /// Баннер расположен на главном экране с кнопкой закрыть
    case profile = "profile"                                        /// Баннер расположен в профиле
    case carousel = "carousel"
    case actual = "actual"
    case banner = "banner"
}

public class GetBannersResponse: Mappable {
    
    public var success: Bool = false
    public var actual: [BannerResponse] = []
    public var carousel: [BannerResponse] = []
    
    public required init?(map: ObjectMapper.Map) {
        self.mapping(map: map)
    }
    
    init?() { }
    
    public func mapping(map: ObjectMapper.Map) {
        success <- (map["success"], anyToBoolTransform)
        actual <- map["actual"]
        carousel <- map["carousel"]
    }
}

public class BannerResponse: Mappable, Hashable, Equatable {
    
    public var bannerId: String = ""
    public var image: String = ""
    public var imageUrl: URL? = nil
    public var type: BannerType?
    public var title: String = ""
    public var description: String = ""
    public var link: String = ""
    public var buttonText: String = ""
    public var buttonBackgroundGradientIdentifier: String = ""
    public var placement: String = ""   // "success_page" || "goals"
    public var isLight: Bool = true     //Кнопка закрыть (темная/светлая)
    public var actionName: String?      //id акции
    
    private var btnBgString: String?
    var btnBgColor: UIColor? {
        guard let hexColor = btnBgString else { return nil }
        return UIColor(hex: hexColor)
    }
    
    private var btnTextString: String?
    var btnTextColor: UIColor? {
        guard let hexColor = btnTextString else { return nil }
        return UIColor(hex: hexColor)
    }
    
    
    //Баннеры с таймером
    var bannerWithTaimer: BannerWithTimer?
    
    //Баннер Черной пятницы
    var blackFridayBanner: BlackFridayBanner?
    
    public required init?(map: ObjectMapper.Map) {
        self.mapping(map: map)
    }
    
    init() { }
    
    public func mapping(map: ObjectMapper.Map) {
        image <- map["image"]
        imageUrl = URL(string: image)
        var typeStr = ""
        typeStr <- map["type"]
        self.type <- (map["type"],EnumTransform<BannerType>())
        
        title <- map["title"]
        description <- map["description"]
        link <- map["deeplink"]
        if link == "" {
            link <- map["link"]
        }
        if link == "" {
            link <- map["deepLink"]
        }
        buttonText <- map["buttonText"]
        placement <- map["placement"]
        bannerId <- map["bannerId"]
        buttonBackgroundGradientIdentifier <- map["gradientIdentifier"]
        btnBgString <- map["btnBgColor"]
        btnTextString <- map["btnTextColor"]
        isLight <- (map["isLight"], anyToBoolTransform)
        
        bannerWithTaimer = BannerWithTimer(map: map)
        btnBgString <- map["blackFriday"]
        actionName <- map["actionName"]
    }
    
    public func hash(into hasher: inout Hasher) {
        hasher.combine(bannerId)
    }
    
    public static func == (lhs: BannerResponse, rhs: BannerResponse) -> Bool {
        lhs.bannerId == rhs.bannerId
    }
}

struct BannerWithTimer: Mappable {
    var burnDate: Date?
    var timerText: String?
    var btnText: String?
    var timerTextColor: String?
    var marketingActivityId: Int?
    
    init?(map: Map) {
        self.mapping(map: map)
    }
    
    mutating func mapping(map: Map) {
        let dateFormatter = DateFormatter.empty
        dateFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSZ"
        var dateString: String = ""
        dateString <- map["burnDate"]
        burnDate = dateFormatter.date(from: dateString)
        if let burnDate = burnDate {
            BannerService.shared.burnDate = burnDate
        }
        timerText <- map["timerText"]
        btnText <- map["btnText"]
        timerTextColor <- map["timerTextColor"]
        marketingActivityId <- map["marketingActivityId"]
    }
}

struct BlackFridayBanner: Mappable {
    
    var mainText: String?
    private var mainTextColorStr: String?
    var mainTextColor: UIColor? {
        guard let hexColor = mainTextColorStr else { return nil }
        return UIColor(hex: hexColor)
    }
    
    var secondaryText: String?
    private var secondaryTextColorStr: String?
    var secondaryTextColor: UIColor? {
        guard let hexColor = secondaryTextColorStr else { return nil }
        return UIColor(hex: hexColor)
    }
    
    var timerPosition: String? // "in_button" / "out_of_button"
    
    var buttonText: String?
    private var buttonTextColorStr: String?
    var buttonTextColor: UIColor? {
        guard let hexColor = buttonTextColorStr else { return nil }
        return UIColor(hex: hexColor)
    }
    private var buttonBackgroundColorStr: String?
    var buttonBackgroundColor: UIColor? {
        guard let hexColor = buttonBackgroundColorStr else { return nil }
        return UIColor(hex: hexColor)
    }
    var buttonDeeplink: String?
    
    var counter: BlackFridayBannerCounter?
    
    init?(map: Map) {
        self.mapping(map: map)
    }
    
    mutating func mapping(map: Map) {
        mainText <- map["mainText"]
        mainTextColorStr <- map["mainTextColor"]
        
        secondaryText <- map["secondaryText"]
        secondaryTextColorStr <- map["secondaryTextColor"]
        
        timerPosition <- map["timerPosition"]
        
        counter <- map["counter"]
        
        buttonText <- map["button.text"]
        buttonTextColorStr <- map["button.textColor"]
        buttonBackgroundColorStr <- map["button.backgroundColor"]
        buttonDeeplink <- map["button.deeplink"]
    }
    
    struct BlackFridayBannerCounter: Mappable {
        var baseCount: Int?
        var secondaryText: String?
        private var textColorStr: String?
        var textColor: UIColor? {
            guard let hexColor = textColorStr else { return nil }
            return UIColor(hex: hexColor)
        }
        var secondaryTextColorStr: String?
        var secondaryTextColor: UIColor? {
            guard let hexColor = secondaryTextColorStr else { return nil }
            return UIColor(hex: hexColor)
        }
        var backgroundColorStr: String?
        var backgroundColor: UIColor? {
            guard let hexColor = backgroundColorStr else { return nil }
            return UIColor(hex: hexColor)
        }
        init?(map: Map) {
            self.mapping(map: map)
        }
        
        mutating func mapping(map: Map) {
            baseCount <- map["baseCount"]
            textColorStr <- map["textColor"]
            secondaryTextColorStr <- map["secondaryTextColor"]
            secondaryText <- map["secondaryText"]
            backgroundColorStr <- map["backgroundColor"]
        }
    }
}
