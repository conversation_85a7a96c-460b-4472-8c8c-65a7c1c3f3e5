//
//  ConfirmChannelResponse.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 18.11.2020.
//  Copyright © 2020 GrowFood. All rights reserved.
//

import Foundation
import ObjectMapper

final class ConfirmChannelResponse: Mappable {
        
    private(set) var id: String = ""
    private(set) var name: String = ""
    private(set) var code: Code = .phone
    
    var icon: UIImage {
        return code.icon
    }
    
    var shortlyName: String {
        let words = name.components(separatedBy: " ")
        guard words.count > 1 else { return name }
        return words.dropFirst().joined(separator: " ")
    }
    
    public required init?(map: ObjectMapper.Map) {
        self.mapping(map: map)
    }
    
    public func mapping(map: ObjectMapper.Map) {
        id <- map["id_H"]
        name <- map["name"]
        
        var codeString = ""
        codeString <- map["code"]
        
        code = Code(rawValue: codeString) ?? .phone
    }
}

extension ConfirmChannelResponse {
    // Список вариантов каналов связи
    enum Code: String {
        case phone, whatsapp, ios_chat, vkontakte, telegram, instagram, facebook
        
        var icon: UIImage {
            switch self {
            case .phone: return #imageLiteral(resourceName: "confirm_phone")
            case .whatsapp: return #imageLiteral(resourceName: "confirm_wa")
            case .ios_chat: return #imageLiteral(resourceName: "confirm_chat")
            case .vkontakte: return #imageLiteral(resourceName: "confirm_vk")
            case .telegram: return #imageLiteral(resourceName: "confirm_tg")
            case .instagram: return #imageLiteral(resourceName: "confirm_ig")
            case .facebook: return #imageLiteral(resourceName: "confirm_fb")
            }
        }
    }
}
