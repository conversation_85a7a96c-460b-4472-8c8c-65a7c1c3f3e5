//
//  ConfirmInterval.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 18.11.2020.
//  Copyright © 2020 GrowFood. All rights reserved.
//

import Foundation
import ObjectMapper

final class ConfirmIntervalResponse: Mappable {
    
    private(set) var id: String = ""
    private(set) var timeTo: String = ""
    private(set) var timeFrom: String = ""
    
    private(set) var timeToString: String = ""
    private(set) var timeFromString: String = ""
    private(set) var session: String = ""
    
    var isNearestInterval: Bool {
        return id == "nearestInterval"
    }
    
    var successPaymentString: String {
        guard !isNearestInterval else { return "в ближайшее время" }
        
        return "с \(timeFrom) до \(timeTo)"
    }
    
    var selectConfirmIntervalString: String {
        return "\(timeFrom) - \(timeTo)"
    }
    
    init(isNearestInterval: Bool) {
        self.id = "nearestInterval"
    }
    
    public required init?(map: ObjectMapper.Map) {
        self.mapping(map: map)
    }
    
    public func mapping(map: ObjectMapper.Map) {
        id <- map["id_H"]
        session <- map["session"]
        timeToString <- map["timeTo"]
        timeFromString <- map["timeFrom"]
        
        let timeToDate = Date(fromString: timeToString, format: .custom("HH:mm:ss"))
        let timeFromDate = Date(fromString: timeFromString, format: .custom("HH:mm:ss"))
        
        timeTo = timeToDate?.toString(format: .custom("HH:mm")) ?? timeToString
        timeFrom = timeFromDate?.toString(format: .custom("HH:mm")) ?? timeFromString
    }
}
