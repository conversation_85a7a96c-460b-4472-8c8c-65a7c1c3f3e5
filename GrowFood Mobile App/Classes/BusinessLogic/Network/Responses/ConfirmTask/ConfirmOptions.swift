//
//  ConfirmOptions.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 18.11.2020.
//  Copyright © 2020 GrowFood. All rights reserved.
//

import Foundation
import ObjectMapper

final class ConfirmOptions: Mappable {
    
    private(set) var channels: [ConfirmChannelResponse] = []
    private(set) var intervals: [ConfirmIntervalResponse] = []
    private(set) var nearestIntervalAvailable: Bool = false
    
    private(set) var notEnoughTime: Bool = false
    private(set) var notEnoughTimeMessage: String = ""
    
    public required init?(map: ObjectMapper.Map) {
        self.mapping(map: map)
    }
    
    public func mapping(map: ObjectMapper.Map) {
        channels <- map["channels"]
        intervals <- map["intervals"]
        nearestIntervalAvailable <- (map["nearestIntervalAvailable"], anyToBoolTransform)
        
        notEnoughTime <- (map["notEnoughTime"], anyToBoolTransform)
        notEnoughTimeMessage <- (map["notEnoughTimeMessage"], anyToStringTransform)
    }
}
