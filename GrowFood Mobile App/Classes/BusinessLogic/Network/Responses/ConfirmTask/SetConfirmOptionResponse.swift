//
//  SetConfirmOptionResponse.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 18.11.2020.
//  Copyright © 2020 GrowFood. All rights reserved.
//

import Foundation
import ObjectMapper

public struct SetConfirmOptionResponse: Mappable {
    
    public var success: Bool = false
    public var order: OrderResponse? = nil
    
    public init?(map: ObjectMapper.Map) {
        self.mapping(map: map)
    }
    
    public mutating func mapping(map: ObjectMapper.Map) {
        success <- (map["success"], anyToBoolTransform)
        order <- map["order"]
    }
}
