//
//  FindAddressesResponse.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 25/09/2018.
//  Copyright © 2018 GrowFood. All rights reserved.
//

import Foundation
import ObjectMapper

public struct FindAddressesResponse: Mappable {
    
    public var success: Bool = false
    public var isAvailable: Bool = false
    public var addresses: [AddressResponse] = [AddressResponse]()
    
    public init?(map: ObjectMapper.Map) {
    }
    
    public mutating func mapping(map: ObjectMapper.Map) {
        success <- (map["success"], anyToBoolTransform)
        isAvailable <- (map["isAvailable"], anyToBoolTransform)
        addresses <- map["suggestions"]
    }
}


