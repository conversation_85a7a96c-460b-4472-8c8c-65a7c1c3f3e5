//
//  GetAddressByCoordsResponse.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 26/09/2018.
//  Copyright © 2018 GrowFood. All rights reserved.
//

import Foundation
import ObjectMapper

public struct GetAddressByCoordsResponse: Mappable {
    
    public var success: Bool = false
    public var isAvailable: Bool = false
    public var address: AddressResponse? = nil
    public var deliveryPrice: Double = 0
    public var availableSessions: [String] = []
    
    public init?(map: ObjectMapper.Map) {
    }
    
    public mutating func mapping(map: ObjectMapper.Map) {
        success <- (map["success"], anyToBoolTransform)
        isAvailable <- (map["isAvailable"], anyToBoolTransform)
        address <- map["suggestion"]
        address?.isAvailable = isAvailable
        deliveryPrice <- (map["deliveryPrice"], anyToDoubleTransform)
        availableSessions <- map["availableSessions"]
        address?.deliveryPrice = deliveryPrice
        address?.availableSessions = availableSessions
    }
}
