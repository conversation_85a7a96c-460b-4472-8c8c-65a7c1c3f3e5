//
//  AddressResponse.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 25/09/2018.
//  Copyright © 2018 GrowFood. All rights reserved.
//

import Foundation
import ObjectMapper

public class AddressResponse: Mappable {
    public var value: String = ""
    public var country: String = ""
    public var city: String = ""
    public var street: String = ""
    public var streetType: String = ""
    public var kladrId: String = ""
    public var blockType: String? = nil
    public var block: String? = nil
    public var house: String? = nil
    public var flat: String? = nil
    public var floor: String? = nil
    public var entrance: String? = nil
    public var lat: Double? = nil
    public var lon: Double? = nil
    public var deliveryPrice: Double = -1
    public var isClientAddress: Bool = false
    public var isAvailable: Bool = false
    public var hasNoFlat: Bool = false
    public var availableSessions: [String] = []
    
    var fullStreet: String {
        var res = "\(streetType) \(street)"
        
        if (house ?? "") != "" {
            res += ", \(house!)"
            if block != nil && blockType != nil {
                res += "\(blockType!)\(block!)"
            }
        }
        if res == " " || res.first ?? " " == " " {
            res = value
        }
        return res
    }
    
    var cityCountry: String {
        var res = city
        if country != "" {
            res += ", \(country)"
        }
        return res
    }
    
    var availabilityTitleForTopPanel: String {
        var result = deliveryPrice == 0 ? "Бесплатно" : deliveryPrice.moneyRoundString
        
        var availSessionsString = ""
        let sessionsCount = availableSessions.count
        if sessionsCount > 0 {
            if sessionsCount == 1 {
                switch availableSessions.first {
                case "morning":
                    availSessionsString = "только утром"
                case "evening":
                    availSessionsString = "только вечером"
                default:
                    availSessionsString = "только вечером"
                }
            }
            else {
                availSessionsString = "утром и вечером"
            }
            result += ": "
            result += availSessionsString
        }
        return result
    }
    
    required public init?(map: ObjectMapper.Map) {
//        self.mapping(map: map)
    }
    
    public func mapping(map: ObjectMapper.Map) {
        
        value <- map["value"]
        city <- map["data.city"]
        country <- map["data.country"]
        street <- map["data.street"]
        house <- map["data.house"]
        blockType <- map["data.block_type"]
        streetType <- map["data.street_type"]
        block <- map["data.block"]
        kladrId <- map["data.region_kladr_id"]
        lat <- (map["data.geo_lat"], anyToDoubleTransform)
        lon <- (map["data.geo_lon"], anyToDoubleTransform)
        var settlement = ""
        settlement <- map["data.settlement_with_type"]
        if settlement != "" {
            if street != "" {
                street = "\(street), \(settlement)"
            }
            else {
                street = settlement
            }
        }
    }
    
    init(address: ClientAddressResponse) {
        value = address.address
        city = address.city
        deliveryPrice = address.deliveryPrice
        isClientAddress = true
        lat = address.lat
        lon = address.lon
        street = address.street
        house = address.house
        flat = address.flat
        hasNoFlat = address.hasNoFlat
        floor = address.floor
        entrance = address.entrance
        isAvailable = true
//        kladrId = UserService.
//        street = address.
    }
    
    var params: [String: Any] {
        var res: [String: Any] = [:]
        
        var curCityId = UserService.shared.allCities.first { cityModel in
            cityModel.kladrId == Int(kladrId)
        }?.cityId
        if curCityId == nil {
            if let intKladrId = Int(kladrId) {
                if intKladrId == 78 {
                    curCityId = 1
                }
                else {
                    curCityId = 2
                }
            }
        }
        res["city_id"] = curCityId ?? UserService.shared.cityId
        res["value"] = value
        res["street"] = street
        res["house"] = house ?? ""
        res["longitude"] = lon ?? 0
        res["latitude"] = lat ?? 0
        res["city"] = city
        res["floor"] = floor ?? ""
        res["flat"] = flat ?? ""
        res["entrance"] = entrance ?? ""
        res["block_type"] = blockType ?? ""
        res["block"] = block ?? ""
        res["hasNoFlat"] = hasNoFlat
        if hasNoFlat {
            res["flat"] = ""
        }
        return res
    }
    
    var unavailableParams: [String: Any] {
        var res: [String: Any] = [:]
        res["value"] = value
        res["street"] = street
        res["house"] = house ?? ""
        res["longitude"] = lon ?? 0
        res["latitude"] = lat ?? 0
        res["city"] = city
        res["floor"] = floor ?? ""
        res["flat"] = flat ?? ""
        res["entrance"] = entrance ?? ""
        res["block_type"] = blockType ?? ""
        res["block"] = block ?? ""
        res["hasNoFlat"] = hasNoFlat
        if hasNoFlat {
            res["flat"] = ""
        }
        return res
    }
    
}
