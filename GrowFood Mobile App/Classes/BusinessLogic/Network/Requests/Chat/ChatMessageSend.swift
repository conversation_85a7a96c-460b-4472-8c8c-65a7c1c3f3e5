//
//  ChatMessageSend.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 11/07/2019.
//  Copyright © 2019 GrowFood. All rights reserved.
//

import Foundation

final class ChatSendRequest: Requestable {
    
    typealias ResponseType = ChatHistoryResponse
    
    let message: String
    let uuid = UUID().uuidString
    
    init(message: String) {
        self.message = message
    }
    
    var endpoint: String {
        return "chat/send"
    }
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return ["message": message,
                "externalId": uuid]
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 60
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
