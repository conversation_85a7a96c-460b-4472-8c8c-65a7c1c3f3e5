//
//  ChatHistoryRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 11/07/2019.
//  Copyright © 2019 GrowFood. All rights reserved.
//

import Foundation

final class ChatHistoryRequest: Requestable {
    
    typealias ResponseType = ChatHistoryResponse
    
    let lastIdH: String?
    
    init(lastIdH: String?) {
        self.lastIdH = lastIdH
    }
    
    var endpoint: String {
        return "chat/history"
    }
    
    var method: Network.Method = .get
    
    var query: Network.QueryType = .path
    
    var parameters: [String : Any]? {
        if lastIdH == nil {
            return [:]
        }
        return ["id_H": lastIdH!]
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 60
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
