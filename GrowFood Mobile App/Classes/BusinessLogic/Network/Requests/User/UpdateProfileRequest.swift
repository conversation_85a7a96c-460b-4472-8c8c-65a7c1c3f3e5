//
//  UpdateProfileRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 05/09/2018.
//  Copyright © 2018 GrowFood. All rights reserved.
//

import Foundation


struct ProfileData {
    let firstName: String?
    let lastName: String?
    let gender: String?
    let email: String?
    let dob: Date?
    let cityId: Int?
    let chatToken: String?
    var token: String? = nil
    
    init(firstName: String? = nil,
         lastName: String? = nil,
         gender: String? = nil,
         email: String? = nil,
         token: String? = nil,
         cityId: Int? = nil,
         dob: Date? = nil,
         chatToken: String? = nil) {
        self.firstName = firstName
        self.lastName = lastName
        self.gender = gender
        self.email = email
        self.cityId = cityId
        self.token = token
        self.dob = dob
        self.chatToken = chatToken
    }
    
    init(userProfileData: UserProfileData) {
        self.firstName = userProfileData.firstName
        self.lastName = userProfileData.lastName
        self.gender = userProfileData.gender
        self.email = userProfileData.email
        self.dob = userProfileData.dob
        self.cityId = nil
        self.chatToken = nil
        self.token =  nil
    }
    
    var params: [String: String] {
        var params: [String: String] = [:]
        
        if firstName != nil {
            params["firstName"] = firstName!
        }
        if lastName != nil {
            params["lastName"] = lastName!
        }
        if gender != nil {
            params["gender"] = gender!
        }
        if email != nil {
            params["email"] = email!
        }
        if chatToken != nil {
            params["chatSessionId"] = chatToken!
        }
        if cityId != nil {
            params["cityId"] = String(cityId!)
        }
        if dob != nil {
            let dateFormatter = DateFormatter.yyyyMMdd
            params["birthday"] = dateFormatter.string(from: dob!)
        }
        return params
    }
    
}

final class UpdateProfileRequest: Requestable {
    
    typealias ResponseType = GetProfileResponse
    
    private var profileData: ProfileData
    private var token: String?
    
    init(profileData: ProfileData, token: String?) {
        self.profileData = profileData
        self.token = token
    }
    
    var endpoint: String {
        return "clients/profile"
    }
    
    var method: Network.Method = .put
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return profileData.params
    }
    
    var headers: [String : String]? {
        var headers = authHeaders
        if let token = token {
            headers["X-Client-Token"] = token
        }
        print(headers)
        return headers
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 20
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
