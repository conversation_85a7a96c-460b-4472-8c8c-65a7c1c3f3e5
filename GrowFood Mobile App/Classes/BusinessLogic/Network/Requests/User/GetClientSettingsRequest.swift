//
//  GetClientSettingsRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 26.10.2021.
//  Copyright © 2021 GrowFood. All rights reserved.
//

import Foundation


final class GetClientSettingsRequest: Requestable {
    
    typealias ResponseType = GetProfileSettingsResponse
    
    var endpoint: String {
        return "clients/settings"
    }
    
    var method: Network.Method = .get
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return nil
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 20
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
