//
//  GetFAQRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 25/12/2018.
//  Copyright © 2018 GrowFood. All rights reserved.
//

import Foundation

final class GetFAQRequest: Requestable {
    
    typealias ResponseType = FAQModel
    
    private let route: String
    
    init(_ type: FAQType) {
        route = type.route
    }
    
    var endpoint: String {
        if UserService.shared.isAuthorized {
            return "faq" + route
        } else {
            return "faq-no-auth/common"
        }
    }
    
    var method: Network.Method = .get
    
    var query: Network.QueryType = .path
    
    var parameters: [String : Any]? {
        if UserService.shared.isAuthorized {
            return nil
        } else {
            return ["cityId": UserService.shared.cityId]
        }
    }
    
    var headers: [String : String]? {
        return UserService.shared.isAuthorized ? authHeaders : notAuthHeaders
    }
    
    var baseUrl: URL {
        return environment.baseURL
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}


