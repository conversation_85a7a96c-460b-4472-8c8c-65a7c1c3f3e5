//
//  GetFAQDeliveryRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 09/09/2019.
//  Copyright © 2019 GrowFood. All rights reserved.
//

import Foundation

final class GetFAQDeliveryRequest: Requestable {
    
    typealias ResponseType = FAQDeliveryResponse
    
    var endpoint: String {
        if UserService.shared.isAuthorized {
            return "faq/delivery"
        }
        else {
            return "faq-no-auth/deliveries"
        }
    }
    
    var method: Network.Method = .get
    
    var query: Network.QueryType = .path
    
    var parameters: [String : Any]? {
        if UserService.shared.isAuthorized {
            return nil
        }
        else {
            return ["cityId": UserService.shared.cityId]
        }
    }
    
    var headers: [String : String]? {
        return UserService.shared.isAuthorized ? authHeaders : notAuthHeaders
    }
    
    var baseUrl: URL {
        return environment.baseURL
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
