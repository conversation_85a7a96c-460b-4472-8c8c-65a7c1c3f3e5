//
//  GetCitiesRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 16/12/2018.
//  Copyright © 2018 GrowFood. All rights reserved.
//

import Foundation

final class GetCitiesRequest: Requestable {
    
    typealias ResponseType = GetCitiesResponse
    
    var endpoint: String {
        return "address-suggest/cities"
    }
    
    var method: Network.Method = .get
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return nil
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 20
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
