//
//  GetClientDisplayableEventsRequest.swift
//  GrowFood Mobile App
//
//  Created by Паве<PERSON> Аристов on 22.07.2021.
//  Copyright © 2021 GrowFood. All rights reserved.
//

final class GetClientDisplayableEventsRequest: Requestable {
    
    enum Category: String {
        case loyalty
    }
    
    typealias ResponseType = GetClientDisplayableEventsResponse
    
    private let category: Category
    
    init(category: Category) {
        self.category = category
    }
    
    var endpoint: String {
        return "client-displayable-events/" + category.rawValue
    }
    
    var method: Network.Method = .get
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return nil
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 20
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
