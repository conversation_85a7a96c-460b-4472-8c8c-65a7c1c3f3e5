//
//  GetReferralInfoRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 31.10.2019.
//  Copyright © 2019 GrowFood. All rights reserved.
//

import Foundation


final class GetReferralInfoRequest: Requestable {
    
    typealias ResponseType = GetReferralInfoResponse
    
    var endpoint: String {
        return "referrals"
    }
    
    var method: Network.Method = .get
    
    var query: Network.QueryType = .path
    
    var parameters: [String : Any]? {
        return ["brandId_H": "lY"]
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return environment.baseURL
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
