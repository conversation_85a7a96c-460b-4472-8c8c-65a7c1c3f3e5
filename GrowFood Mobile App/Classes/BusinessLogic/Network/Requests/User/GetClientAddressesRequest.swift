//
//  GetClientAddressesRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 19/10/2018.
//  Copyright © 2018 GrowFood. All rights reserved.
//

import Foundation

final class GetClientAddressesRequest: Requestable {
    
    typealias ResponseType = GetClientAddressesResponse
    
    var endpoint: String {
        return "clients/addresses"
    }
    
    var method: Network.Method = .get
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return nil
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 20
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
