//
//  RegisterPushTokenRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 13/12/2018.
//  Copyright © 2018 GrowFood. All rights reserved.
//

import Foundation

final class RegisterPushTokenRequest: Requestable {
    
    typealias ResponseType = GetClientAddressesResponse
    
    private var pushToken: String
    
    init(pushToken: String) {
        self.pushToken = pushToken
    }
    
    var endpoint: String {
        return "push/createToken"
    }
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        let params: [String : Any] = ["pushToken": pushToken,
                                      "playerId": "0"]
        return params
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 20
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
