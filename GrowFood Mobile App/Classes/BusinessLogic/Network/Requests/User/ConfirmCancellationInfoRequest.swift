//
//  ConfirmCancellationInfoRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 06/09/2019.
//  Copyright © 2019 GrowFood. All rights reserved.
//

import Foundation

final class ConfirmCancellationInfoRequest: Requestable {
    
    typealias ResponseType = ConfirmCancellationInfoResponse
    
    var endpoint: String {
        return "client-details/confirm-cancellation-info"
    }
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return nil
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 20
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
