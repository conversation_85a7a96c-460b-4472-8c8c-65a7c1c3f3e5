//
//  DeleteAccountRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 14.06.2022.
//  Copyright © 2022 GrowFood. All rights reserved.
//

import Foundation

final class DeleteAccountRequest: Requestable {
    
    typealias ResponseType = DeleteAccountResponse
    
    var endpoint: String {
        return "clients/request-delete"
    }
    
    var method: Network.Method = .put
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        var params: [String : Any]? = nil
        if let user = UserService.shared.curUser {
            params = ["userId": user.id]
        }
        return params
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 20
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}

