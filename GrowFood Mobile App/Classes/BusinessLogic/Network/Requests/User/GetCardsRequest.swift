//
//  GetCardsRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 19/09/2018.
//  Copyright © 2018 GrowFood. All rights reserved.
//

import Foundation

final class GetCardsRequest: Requestable {
    
    typealias ResponseType = GetCardsResponse
    
    private let withVirtual: Bool
    
    init(withVirtual: Bool = false) {
        self.withVirtual = withVirtual
    }
    
    var endpoint: String {
        return "cards"
    }
    
    var method: Network.Method = .get
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return [
            "withVirtual": withVirtual ? 1 : 0
        ]
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 20
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}

