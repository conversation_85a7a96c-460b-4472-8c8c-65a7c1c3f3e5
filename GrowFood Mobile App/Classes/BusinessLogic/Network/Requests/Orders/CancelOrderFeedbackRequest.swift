//
//  CancelOrderFeedbackRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 28.11.2019.
//  Copyright © 2019 GrowFood. All rights reserved.
//

import Foundation

final class CancelOrderFeedbackRequest: Requestable {
    
    typealias ResponseType = NewOrderResponse
    
    var reasonIdHs: [String]
    var orderIdH: String
    var comment: String
    
    init(reasonIdHs: [String],
         orderIdH: String,
         comment: String) {
        self.reasonIdHs = reasonIdHs
        self.orderIdH = orderIdH
        self.comment = comment
    }
    
    var endpoint: String {
        return "orders/feedback-for-cancel"
    }
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        let feedbackArr = ["reasonIds_HA" : reasonIdHs,
                           "comment": comment] as [String : Any]
        return ["orderId_H": orderIdH,
                "feedback": feedbackArr]
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 20
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
    
}
