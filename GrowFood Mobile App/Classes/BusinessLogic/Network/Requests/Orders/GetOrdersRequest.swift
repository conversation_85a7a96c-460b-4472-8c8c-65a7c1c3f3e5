//
//  GetOrdersRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 07/09/2018.
//  Copyright © 2018 GrowFood. All rights reserved.
//

import Foundation

final class GetOrdersRequest: Requestable {
    
    typealias ResponseType = GetOrdersResponse
    
    var endpoint: String {
        return "orders/active"
    }
    
    var method: Network.Method = .get
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? = nil
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 60
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
