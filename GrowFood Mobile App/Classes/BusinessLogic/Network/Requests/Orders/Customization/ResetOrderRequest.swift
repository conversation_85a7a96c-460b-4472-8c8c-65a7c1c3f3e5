//
//  ResetOrderRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 12/02/2019.
//  Copyright © 2019 GrowFood. All rights reserved.
//

import Foundation

final class ResetOrderRequest: Requestable {
    
    typealias ResponseType = NewOrderResponse
    
    var orderIdH: String
    
    init(orderIdH: String) {
        self.orderIdH = orderIdH
    }
    
    var endpoint: String {
        return "orders/customization/drop"
    }
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        let params: [String : Any] = ["orderId_H": orderIdH]
        
        return params
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 40
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
    
}
