//
//  AcceptOrderRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 28/12/2018.
//  Copyright © 2018 GrowFood. All rights reserved.
//

import Foundation

final class AcceptOrderRequest: Requestable {
    
    typealias ResponseType = NewOrderResponse
    
    var orderIdH: String
    
    init(orderIdH: String) {
        self.orderIdH = orderIdH
    }
    
    var endpoint: String {
        return "orders/customization/accept"
    }
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        let params: [String : Any] = ["orderId_H": orderIdH]
        
        return params
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 40
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
    
}
