//
//  DoCustomizationRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 28/12/2018.
//  Copyright © 2018 GrowFood. All rights reserved.
//

import Foundation

final class DoCustomizationRequest: Requestable {
    
    typealias ResponseType = DoCustomizationResponse
    
    var customizationInfo : [[String: Any]]
    var orderIdH: String
    
    init(customizationInfo : [[String: Any]],
         orderIdH: String) {
        self.customizationInfo = customizationInfo
        self.orderIdH = orderIdH
    }
    
    var endpoint: String {
        return "orders/customization/add"
    }
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        var params: [String : Any] = ["orderId_H": orderIdH]
        params["customPacks"] = customizationInfo
        return params
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 40
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
    
}

final class DoReplacesItemsRequest: Requestable {
    
    typealias ResponseType = NewOrderResponse
    
    var changes: [[String: Any]]
    var orderIdH: String
    var filterIds: [Int]?
    
    init(changes: [[String: Any]],
         orderIdH: String,
         filterIds: [Int]?) {
        self.changes = changes
        self.orderIdH = orderIdH
        self.filterIds = filterIds
    }
    
    var endpoint: String {
        if filterIds != nil {
            return "orders/customization/filter"
        }
        else {
            return "orders/customization/add"
        }
    }
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        var params: [String : Any] = ["orderId_H": orderIdH]
        
        var packs = Array<Dictionary<String, Any>>()
        
        changes.forEach { (changeItem) in
//            let item = ["remove": changeItem.remove,
//                        "mealDate": changeItem.mealDateString,
//                        "mealNumber": changeItem.mealNumber,
//                        "packId": changeItem.packId] as [String : Any]
            packs.append(changeItem)
        }
        if packs.count > 0 {
            params["customPacks"] = Array(packs.reversed())
        }
        if let filterIds = self.filterIds {
            params["filterIds"] = filterIds
        }
        else {
            params["filterIds"] = NSNull()
        }
        
        return params
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 40
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
    
}
