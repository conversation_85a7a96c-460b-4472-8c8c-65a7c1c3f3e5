//
//  AddDrinksRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 15.04.2020.
//  Copyright © 2020 GrowFood. All rights reserved.
//

import Foundation

final class AddDrinksRequest: Requestable {
    
    typealias ResponseType = AddDrinksResponse
    
    var orderIdH: String
    
    init(orderIdH: String) {
        self.orderIdH = orderIdH
    }
    
    var endpoint: String {
        return "orders/add-drinks"
    }
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        let params: [String : Any] = ["orderId_H": orderIdH]
        
        return params
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 40
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
    
}
