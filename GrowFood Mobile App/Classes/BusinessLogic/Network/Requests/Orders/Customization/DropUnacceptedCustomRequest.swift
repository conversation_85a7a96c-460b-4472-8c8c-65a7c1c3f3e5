//
//  DropUnacceptedCustomRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 29.05.2020.
//  Copyright © 2020 GrowFood. All rights reserved.
//

import Foundation

final class DropDrinksRequest: Requestable {
    
    typealias ResponseType = NewOrderResponse
    
    var orderIdH: String
    
    init(orderIdH: String) {
        self.orderIdH = orderIdH
    }
    
    var endpoint: String {
        return "orders/drop-drinks"
    }
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        let params: [String : Any] = ["orderId_H": orderIdH]
        
        return params
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 40
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
    
}
