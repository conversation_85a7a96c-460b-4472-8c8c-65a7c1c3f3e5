//
//  PayDetailsOrder.swift
//  GrowFood Mobile App
//
//  Created by Medyannik Dmitri on 18.03.2022.
//  Copyright © 2022 GrowFood. All rights reserved.
//

import Foundation

final class PayDetailsOrder: Requestable {
    
    typealias ResponseType = PayDetailsOrderResponse
    
    private var orderId: String
    /// Передавать null, если ничего не известно о бонусах, 0 и число - считаются за отмену/применение бонусов
    private var bonusesToApply: Int?
    
    init(orderId: String, bonusesToApply: Int?) {
        self.orderId = orderId
        self.bonusesToApply = bonusesToApply
    }
    
    var endpoint: String {
        return "orders/pay-details"
    }
    
    var method: Network.Method = .get
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        var param: [String: Any] = ["orderId_H": orderId]
        
        if let bonusesToApply = bonusesToApply {
            param["bonusesToApply"] = bonusesToApply
        }
        return param
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
