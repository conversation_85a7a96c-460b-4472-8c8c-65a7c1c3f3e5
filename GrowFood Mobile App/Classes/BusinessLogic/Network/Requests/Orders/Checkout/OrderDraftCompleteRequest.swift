//
//  OrderDraftCompleteRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 02.07.2024.
//  Copyright © 2024 GrowFood. All rights reserved.
//

import Foundation

final class OrderDraftCompleteRequest: Requestable {
    
    typealias ResponseType = NewOrderResponse
    
    private var orderIdH: String
    private var isAutolongationEnabled: Bool
    
    init(orderIdH: String, isAutolongationEnabled: Bool) {
        self.orderIdH = orderIdH
        self.isAutolongationEnabled = isAutolongationEnabled
    }
    
    var endpoint: String {
        return "orders/draft/complete"
    }
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return ["orderId_H": orderIdH,
                "isAutolongationEnabled": isAutolongationEnabled]
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
