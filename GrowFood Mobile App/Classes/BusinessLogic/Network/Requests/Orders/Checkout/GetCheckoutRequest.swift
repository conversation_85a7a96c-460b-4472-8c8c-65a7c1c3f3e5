//
//  GetCheckoutRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 14.09.2023.
//  Copyright © 2023 GrowFood. All rights reserved.
//

import Foundation

final class GetCheckoutRequest: Requestable {
    
    typealias ResponseType = GetOrderCheckoutResponse
    
    private var orderId: String
    
    init(orderId: String) {
        self.orderId = orderId
    }
    
    var endpoint: String {
        return "orders/checkout"
    }
    
    var method: Network.Method = .get
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return ["orderId_H": orderId]
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
