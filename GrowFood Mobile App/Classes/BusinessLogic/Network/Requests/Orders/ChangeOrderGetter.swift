//
//  ChangeOrderGetter.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 18.06.2020.
//  Copyright © 2020 GrowFood. All rights reserved.
//

import Foundation

final class ChangeOrderGetter: Requestable {
    
    typealias ResponseType = NewOrderResponse
    
    var orderIdH: String
    var familyMemberId: String
    var firstName: String
    var phone: String
//    {
//      "orderId_H": "zVx",
//      "familyMemberId": "zVx",
//      "firstName": "Ирина",
//      "phone": "79112223344"
//    }
    init(orderIdH: String,
         familyMemberId: String,
         firstName: String,
         phone: String) {
        self.orderIdH = orderIdH
        self.familyMemberId = familyMemberId
        self.firstName = firstName
        self.phone = phone
    }
    
    var endpoint: String {
        return "family/leave-order-to-member"
    }
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return ["orderId_H": orderIdH,
                "familyMemberId": familyMemberId,
                "firstName": firstName,
                "phone": phone]
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 20
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
    
}
