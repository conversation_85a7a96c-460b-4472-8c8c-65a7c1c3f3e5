//
//  ChangeTariffRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 08/11/2018.
//  Copyright © 2018 GrowFood. All rights reserved.
//

import Foundation

struct ChangeTariffData {
    var pricePlanId: Int = 0
    var type: String = ""
    var mealDays: Int = 0
    init(pricePlanId: Int,
         type: String,
         mealDays: Int) {
        self.pricePlanId = pricePlanId
        self.type = type
        self.mealDays = mealDays
    }
    
    var postParams: [String: Any] {
        var params = ["pricePlanId":pricePlanId]
        if type == "atomized" || type == "by_packs" {
            params["mealDays"] = mealDays
        }
        return params
    }
}

final class ChangeTariffRequest: Requestable {
    
    typealias ResponseType = NewOrderResponse
    
    private var changeTariffData: ChangeTariffData
    private var orderId: String
    
    init(changeTariffData: ChangeTariffData,
         orderId: String) {
        self.changeTariffData = changeTariffData
        self.orderId = orderId
    }
    
    var endpoint: String {
        return "orders/price-plan"
    }
    
    var method: Network.Method = .put
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        var params: [String : Any] = changeTariffData.postParams
        params["orderId_H"] = orderId
        
        return params
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
