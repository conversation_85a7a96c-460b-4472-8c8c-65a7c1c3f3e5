//
//  CancelOrderWithAutoLongate.swift
//  GrowFood Mobile App
//
//  Created by d.medyannik on 10.01.2025.
//  Copyright © 2025 GrowFood. All rights reserved.
//

import Foundation

final class CancelOrderWithAutoLongate: Requestable {
    
    typealias ResponseType = CancelOrderResponse
    
    var orderIdH: String
    
    init(orderIdH: String) {
        self.orderIdH = orderIdH
    }
    
    var endpoint: String {
        return "orders/cancel-with-autolongation"
    }
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return ["orderId_H": orderIdH]
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 20
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
    
}
