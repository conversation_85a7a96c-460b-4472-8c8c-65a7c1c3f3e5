//
//  GetFullMenuForOrderRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 02.09.2020.
//  Copyright © 2020 GrowFood. All rights reserved.
//

import Foundation

final class GetFullMenuForOrderRequest: Requestable {
    
    typealias ResponseType = GetCustomMenuResponse
    
    private var orderId: String
    private var requiredFilters: Bool // отправлять ли принудительно фильтры
    var filters: [Int]
    
    init(orderId: String,
         filters: [Int],
         requiredFilters: Bool) {
        self.orderId = orderId
        self.filters = filters
        self.requiredFilters = requiredFilters
    }
    
    var endpoint: String {
        return "menu/customization"
    }
    
    var method: Network.Method = .get
    
    var query: Network.QueryType = .path
    
    var parameters: [String : Any]? {
        var params = ["orderId_H": orderId] as [String : Any]
        if requiredFilters {
            params["filterIds"] = Array<Int>()
        }
        if filters.count > 0 {
            params["filterIds"] = filters
        }
        return params
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
