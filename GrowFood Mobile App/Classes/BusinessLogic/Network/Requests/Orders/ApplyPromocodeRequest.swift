//
//  ApplyPromocodeRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 09/10/2018.
//  Copyright © 2018 GrowFood. All rights reserved.
//

import Foundation

final class ApplyPromocodeRequest: Requestable {
    
    typealias ResponseType = NewOrderResponse
    
    var promocode: String
    var orderIdH: String
    
    init(promocode: String,
         orderIdH: String) {
        self.promocode = promocode
        self.orderIdH = orderIdH
    }
    
    var endpoint: String {
        return "orders/promocode/apply"
    }
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return ["orderId_H": orderIdH,
                "promocode": promocode]
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 20
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
    
}
