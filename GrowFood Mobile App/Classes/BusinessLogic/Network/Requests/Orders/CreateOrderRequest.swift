//
//  CreateOrderRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 02.03.2023.
//  Copyright © 2023 GrowFood. All rights reserved.
//

import Foundation

struct CreateOrderData {
    var newOrderViewModel: NewOrderViewModel!
    let menuModel: NewOrderMenuViewModel?
    let menuTypesCatalogConfigItemId: String?
    let clientAddressIdH: String?
    let familyId: String?
    
    init(newOrderViewModel: NewOrderViewModel,
         menu: NewOrderMenuViewModel?,
         menuTypesCatalogConfigItemId: String?,
         familyId: String?) {
        self.newOrderViewModel = newOrderViewModel
        self.menuModel = menu
        self.menuTypesCatalogConfigItemId = menuTypesCatalogConfigItemId
        self.clientAddressIdH = UserService.shared.curUserSettings?.userConfig?.defaultSettings?.address?.idH
        self.familyId = familyId
    }
    
    var paramsDict: [String: Any] {
        var params: [String: Any] = ["pricePlanId": newOrderViewModel.menuTypeModel.pricePlanId]
        params["mealDays"] = newOrderViewModel.currentMealDay?.numberOfMealDaysForMenu ?? newOrderViewModel.menuTypeModel.defaultNumberOfMealDays
        if let finalPrice = menuModel?.priceWithDiscount {
            params["predictionPrice"] = finalPrice
        }
        if clientAddressIdH != nil {
            params["clientAddressId_H"] = clientAddressIdH!
        }
        return params
    }
}

final class CreateOrderRequest: Requestable {
    
    typealias ResponseType = NewOrderResponse
    
    private var newOrderData: CreateOrderData
    private var promocode: String?
    private var familyId: String?
    
    init(newOrderData: CreateOrderData,
         promocode: String?,
         familyId: String?) {
        self.newOrderData = newOrderData
        self.promocode = promocode
        self.familyId = familyId
    }
    
    var endpoint: String {
        return Feature.isOn(.isDraftOrderFlowEnabled) ? "orders/draft" : "orders"
    }
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        var params: [String : Any] = [:]
        
        params["order"] = newOrderData.paramsDict
        if let familyIdH = familyId {
            params["familyMember"] = ["id_H": familyIdH]
        } 
        if promocode != nil && promocode != "" {
            params["promocode"] = promocode!
        }
        if let subscriptionIdH = newOrderData.newOrderViewModel.currentMealDay?.subscriptionIdH {
            params["subscriptionId_H"] = subscriptionIdH
        }
        if let utmDate = GFUserDefaults.shared.utmCreationDate, let utmDict = GFUserDefaults.shared.utmValue {
            if daysBetweenDates(startDate: utmDate, endDate: Date()) < 7 {
                params["analyticalData"] = utmDict
            }
        } else if let afParams = GFUserDefaults.shared.afFull {
            params["analyticalData"] = afParams
        }
        if let appMetricaId = GFAnalytics.shared.appMetricaId {
            params["appMetricaId"] = appMetricaId
        }
        if let menuCatalogSectionId = newOrderData.menuTypesCatalogConfigItemId {
            params["menuTypesCatalogConfigItemId"] = menuCatalogSectionId
        }
        
        var deliveryParams: [String: Any] = [:]
        
        let dateFormatter = DateFormatter.yyyyMMdd
        let deliveryData = newOrderData.newOrderViewModel.currentDeliveryData
        deliveryParams["date"] = dateFormatter.string(from: deliveryData.date)
        deliveryParams["session"] = deliveryData.session.rawValue
        if deliveryData.from != "" {
            deliveryParams["timeFrom"] = deliveryData.from
            deliveryParams["timeTo"] = deliveryData.to
        }
        params["firstDelivery"] = deliveryParams
        if let customPacks = newOrderData.menuModel?.customizationInfo {
            params["customizationInfo"] = ["custom_packs": customPacks]
        }
        
        if !newOrderData.newOrderViewModel.currentComponentFilters.isEmpty {
            params["filterIds"] = newOrderData.newOrderViewModel.currentComponentFilters.map { $0.id }
        }
        params["isBmiOrder"] = newOrderData.newOrderViewModel.menuTypeModel.useClientsBmi
        var templateIds: [Int] = []
        if let templates = UserService.shared.curUser?.menuCalendarConfig {
            if let mealsTemplateId = templates.mealsTemplateId {
                templateIds.append(mealsTemplateId)
            }
        }
        if templateIds.count > 0 {
            params["appliedMealFilterTemplateIds"] = templateIds
        }
        return params
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}


func daysBetweenDates(startDate: Date, endDate: Date) -> Int {
    let calendar = Calendar.current
    let components = calendar.dateComponents([Calendar.Component.day], from: startDate, to: endDate)
    return components.day!
}
