//
//  ChangeAutoLongate.swift
//  GrowFood Mobile App
//
//  Created by d.medyannik on 20.12.2024.
//  Copyright © 2024 GrowFood. All rights reserved.
//

import Foundation

final class ChangeAutoLongate: Requestable {
    
    typealias ResponseType = BaseSuccessResponse
    
    var isEnable: Bool
    var orderIdH: String
    
    init(isEnable: Bool,
         orderIdH: String) {
        self.isEnable = isEnable
        self.orderIdH = orderIdH
    }
    
    var endpoint: String {
        return "orders/change-autolongate"
    }
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return ["orderId_H": orderIdH,
                "isEnable": isEnable]
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 20
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
    
}
