//
//  GetOrderDetailRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 24.11.2020.
//  Copyright © 2020 GrowFood. All rights reserved.
//

import Foundation

final class GetOrderDetailRequest: Requestable {
    
    typealias ResponseType = GetOrderDetailResponse
    
    private let orderIdH: String
    
    init(orderIdH: String) {
        self.orderIdH = orderIdH
    }
    
    var endpoint: String {
        return "orders"
    }
    
    var method: Network.Method = .get
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return ["orderIds_HU": orderIdH]
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 60
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
