//
//  SetDeliveryTimeRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 02/10/2018.
//  Copyright © 2018 GrowFood. All rights reserved.
//

import Foundation

final class SetDeliveryTimeData {
    
    var deliveryId: String
    var date: Date
    var timeFrom: String
    var timeTo: String
    var session: DeliveryDay.DeliveryType
    
    init(deliveryId: String,
         date: Date,
         timeFrom: String,
         timeTo: String,
         session: DeliveryDay.DeliveryType) {
        
        self.deliveryId = deliveryId
        self.date = date
        self.timeFrom = timeFrom
        self.timeTo = timeTo
        self.session = session
    }
    
    var params: [String: Any] {
        var res: [String: Any] = [:]
        res["deliveryId_H"] = deliveryId
        res["timeFrom"] = timeFrom
        res["timeTo"] = timeTo
        res["session"] = session.rawValue
        let dateFormatter = DateFormatter.yyyyMMdd
        res["date"] = dateFormatter.string(from: date)
        return res
    }
    
}


final class SetDeliveryTimeRequest: Requestable {
    
    typealias ResponseType = NewOrderResponse
    
    var timeData: SetDeliveryTimeData
    
    init(timeData: SetDeliveryTimeData) {
        self.timeData = timeData
    }
    
    var endpoint: String {
        return "deliveries/update-date"
    }
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return timeData.params
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
    
}
