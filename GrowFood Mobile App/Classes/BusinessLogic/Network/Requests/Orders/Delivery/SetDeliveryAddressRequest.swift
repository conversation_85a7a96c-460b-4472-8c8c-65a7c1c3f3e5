//
//  SetDeliveryAddressRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 01/10/2018.
//  Copyright © 2018 GrowFood. All rights reserved.
//

import Foundation

final class SetAddressData {
    
    var deliveryId: String
    var address: AddressResponse
    
    init(deliveryId: String,
         address: AddressResponse) {
        self.deliveryId = deliveryId
        self.address = address
    }
    
    var params: [String: Any] {
        var res: [String: Any] = [:]
        res["deliveryId_H"] = deliveryId
        res["addressInfo"] = address.params
        return res
    }
    
}


final class SetDeliveryAddressRequest: Requestable {
    
    typealias ResponseType = SetAddressResponse
    
    var addressData: SetAddressData
    
    init(addressData: SetAddressData) {
        self.addressData = addressData
    }
    
    var endpoint: String {
        return "deliveries/address/add"
    }
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return addressData.params
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
    
}
