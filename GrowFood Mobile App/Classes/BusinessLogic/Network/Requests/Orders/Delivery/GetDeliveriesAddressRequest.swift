//
//  GetDeliveriesAddressRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 25.09.2023.
//  Copyright © 2023 GrowFood. All rights reserved.
//

import Foundation

final class GetDeliveriesAddressRequest: Requestable {
    
    typealias ResponseType = GetClientAddressesResponse
    
    var deliveryId: String
    
    init(deliveryId: String) {
        self.deliveryId = deliveryId
    }
    
    var endpoint: String {
        return "deliveries/address"
    }
    
    var method: Network.Method = .get
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return ["deliveryId_H": deliveryId]
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 20
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
