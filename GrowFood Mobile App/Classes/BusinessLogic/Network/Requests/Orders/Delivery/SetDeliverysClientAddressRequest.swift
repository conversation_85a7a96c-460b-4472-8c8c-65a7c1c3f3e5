//
//  SetDeliverysClientAddressRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 25.06.2024.
//  Copyright © 2024 GrowFood. All rights reserved.
//

import Foundation

final class SetDeliverysClientAddressRequest: Requestable {
    
    typealias ResponseType = SetAddressResponse
    
    var addressId: String
    var deliveryId: String
    var session: String?
    var timeFrom: String?
    var timeTo: String?
    var setForNextDeliveries: Bool?
    
    init(addressId: String,
         deliveryId: String,
         session: String?,
         timeFrom: String?,
         timeTo: String?,
         setForNextDeliveries: Bool?) {
        self.addressId = addressId
        self.deliveryId = deliveryId
        self.session = session
        self.timeFrom = timeFrom
        self.timeTo = timeTo
        self.setForNextDeliveries = setForNextDeliveries
        
    }
    
    var endpoint: String {
        return "deliveries/address/set"
    }
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        var param: [String : Any] = ["deliveryId_H": deliveryId,
                                     "clientAddressId_H": addressId]
        if let session {
            param["session"] = session
        }
        if let timeFrom {
            param["timeFrom"] = timeFrom
        }
        if let timeTo {
            param["timeTo"] = timeTo
        }
        if let setForNextDeliveries {
            param["setForNextDeliveries"] = setForNextDeliveries
        }
        return param
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}

