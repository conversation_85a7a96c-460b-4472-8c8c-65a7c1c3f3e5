//
//  SetDeliveryClientAddressRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 01/11/2018.
//  Copyright © 2018 GrowFood. All rights reserved.
//

import Foundation

final class SetDeliveryClientAddressRequest: Requestable {
    
    typealias ResponseType = SetAddressResponse
    
    var addressId: String
    var deliveryId: String
    
    init(addressId: String,
         deliveryId: String) {
        self.addressId = addressId
        self.deliveryId = deliveryId
    }
    
    var endpoint: String {
        return "deliveries/address/set"
    }
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return ["deliveryId_H": deliveryId,
                "clientAddressId_H": addressId]
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
    
}
