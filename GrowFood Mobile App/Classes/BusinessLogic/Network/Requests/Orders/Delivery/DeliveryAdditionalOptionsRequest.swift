import Foundation

struct DeliveryAdditionalOptionsRequest: Requestable {
    
    typealias ResponseType = DeliveryAdditionalOptionsResponse
    
    var endpoint: String = "deliveries/options/additional-options"
    
    var method: Network.Method = .get
    
    var query:  Network.QueryType = .json
    
    var parameters: [String: Any]?
    
    var headers: [String: String]? {
        authHeaders
    }
    
    var baseUrl: URL {
        defaultBaseUrl
    }
    
    var timeout : TimeInterval = 50
    
    var cachePolicy : NSURLRequest.CachePolicy = .reloadIgnoringLocalAndRemoteCacheData
    
    init(deliveryId: String,
         optionId: String,
         remove: Bool) {
        
        parameters = [
            Keys.deliveryId.rawValue: deliveryId,
            Keys.optionId.rawValue: optionId,
            Keys.remove.rawValue: remove
        ]
    }
}


private extension DeliveryAdditionalOptionsRequest {
    enum Keys: String {
        case deliveryId = "deliveryId_H"
        case optionId = "optionId_H"
        case remove
    }
}



struct ApplyDeliveryAdditionalOptionsRequest: Requestable {
    
    typealias ResponseType = NewOrderResponse
    
    var endpoint: String = "deliveries/options/additional-options/apply"
    
    var method: Network.Method = .post
    
    var query:  Network.QueryType = .json
    
    var parameters: [String: Any]?
    
    var headers: [String: String]? {
        authHeaders
    }
    
    var baseUrl: URL {
        defaultBaseUrl
    }
    
    var timeout : TimeInterval = 50
    
    var cachePolicy : NSURLRequest.CachePolicy = .reloadIgnoringLocalAndRemoteCacheData
    
    init(deliveryId: String,
         optionId: String,
         additionalOptionId: String,
         remove: Bool) {
        
        parameters = [
            Keys.deliveryId.rawValue: deliveryId,
            Keys.optionId.rawValue: optionId,
            Keys.additionalOptionId.rawValue: additionalOptionId,
            Keys.remove.rawValue: remove
        ]
    }
}


private extension ApplyDeliveryAdditionalOptionsRequest {
    enum Keys: String {
        case deliveryId = "deliveryId_H"
        case optionId = "optionId_H"
        case additionalOptionId = "additionalOptionId_H"
        case remove
    }
}
