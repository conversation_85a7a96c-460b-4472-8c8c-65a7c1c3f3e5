//
//  CancelDeliveryRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 06/09/2019.
//  Copyright © 2019 GrowFood. All rights reserved.
//

import Foundation

final class CancelDeliveryRequest: Requestable {
    
    typealias ResponseType = DeliveryCancelResponse
    
    var deliveryId: String
    
    init(deliveryId: String) {
        self.deliveryId = deliveryId
    }
    
    var endpoint: String {
        return "deliveries/cancel"
    }
    
    var method: Network.Method {
        return  .post
    }
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return ["deliveryId_H": deliveryId]
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
    
}
