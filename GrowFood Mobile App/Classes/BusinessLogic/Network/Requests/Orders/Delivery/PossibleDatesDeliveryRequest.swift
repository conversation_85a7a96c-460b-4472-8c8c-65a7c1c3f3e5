//
//  PossibleDatesDeliveryRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON>dy<PERSON><PERSON> on 20.07.2021.
//  Copyright © 2021 GrowFood. All rights reserved.
//

import Foundation

final class PossibleDatesDeliveryRequest: Requestable {
    
    typealias ResponseType = GetPossbleDatesDeliveryResponse
    
    var deliveryId: String
    
    init(deliveryId: String) {
        self.deliveryId = deliveryId
    }
    
    var endpoint: String {
        return "deliveries/possible-dates"
    }
    
    var method: Network.Method {
        return  .get
    }
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return ["deliveryId_H": deliveryId]
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
    
}
