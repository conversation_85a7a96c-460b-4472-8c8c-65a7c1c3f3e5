//
//  DeliveryOptionsRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 12.12.2019.
//  Copyright © 2019 GrowFood. All rights reserved.
//

import Foundation


final class DeliveryOptionEditData {
    
    enum DeliveryOptionEditType {
        case add, update, remove
        
        func method() -> Network.Method {
            switch self {
            case .remove:
                return .delete
            default:
                return .post
            }
        }
    }
    
    var toOrder: Bool = false
    var orderIdH: String = ""
    var deliveryIdH: String = ""
    var comment: String = ""
    var optionIdH: String = ""
    var type: DeliveryOptionEditType = .add
    
    
    var endPoint: String {
        switch type {
        case .update:
            return "deliveries/options/update"
        default:
            if toOrder {
                return "deliveries/options/orders"
            }
            else {
                return "deliveries/options"
            }
        }
    }
    
    var params: [String: Any] {
        if toOrder == false {
            return ["optionId_H": optionIdH,
                    "deliveryId_H": deliveryIdH,
                    "comment": comment]
        }
        else {
            return ["optionId_H": optionIdH,
                    "orderId_H": orderIdH]
        }
    }
}

final class SetDeliveryOptionsRequest: Requestable {
    
    typealias ResponseType = DeliveryCancelResponse
    
    var editData: DeliveryOptionEditData
    
    init(editData: DeliveryOptionEditData) {
        self.editData = editData
    }
    
    var endpoint: String {
        return editData.endPoint
    }
    
    var method: Network.Method {
        return  editData.type.method()
    }
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return editData.params
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
    
}
