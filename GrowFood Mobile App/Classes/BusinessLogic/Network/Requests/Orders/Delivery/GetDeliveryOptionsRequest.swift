//
//  GetDeliveryOptionsRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 30.09.2021.
//  Copyright © 2021 GrowFood. All rights reserved.
//

import Foundation

final class GetDeliveryOptionsRequest: Requestable {
    
    typealias ResponseType = GetDeliveryOptionsResponse
    
    var deliveryId: String
    
    init(deliveryId: String) {
        self.deliveryId = deliveryId
    }
    
    var endpoint: String {
        return "deliveries/options/possible"
    }
    
    var method: Network.Method {
        return  .get
    }
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return ["deliveryId_H": deliveryId]
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
    
}
