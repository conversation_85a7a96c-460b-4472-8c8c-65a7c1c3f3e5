//
//  LongateOrder.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 15/10/2018.
//  Copyright © 2018 GrowFood. All rights reserved.
//

import Foundation

final class LongateOrderRequest: Requestable {
    
    typealias ResponseType = LongateOrderResponse
    
    private var orderId: String
    
    init(orderId: String) {
        self.orderId = orderId
    }
    
    var endpoint: String {
        return "orders/longate"
    }
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        if let id = Int(orderId) {
            return ["orderId": id]
        }
        let params = ["orderId_H": orderId]
        return params
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
