//
//  CancelOrderRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 14/12/2018.
//  Copyright © 2018 GrowFood. All rights reserved.
//

import Foundation

final class CancelOrderRequest: Requestable {
    
    typealias ResponseType = CancelOrderResponse
    
    private var orderId: String
    
    init(orderId: String) {
        self.orderId = orderId
    }
    
    var endpoint: String {
        return "orders/cancel"
    }
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        let params: [String : Any] = ["orderId_H": orderId]
        return params
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
