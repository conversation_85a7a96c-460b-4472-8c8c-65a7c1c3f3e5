//
//  ApplyAutoLongateRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 04/02/2019.
//  Copyright © 2019 GrowFood. All rights reserved.
//

import Foundation

final class ApplyAutoLongateRequest: Requestable {
    
    typealias ResponseType = NewOrderResponse
    
    var apply: Bool
    var orderIdH: String
    
    init(apply: Bool,
         orderIdH: String) {
        self.apply = apply
        self.orderIdH = orderIdH
    }
    
    var endpoint: String {
        return "orders/update-subscription"
    }
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return ["orderId_H": orderIdH,
                "isAutoPaid": apply,
                "autolongationDiscount": apply]
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 20
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
    
}
