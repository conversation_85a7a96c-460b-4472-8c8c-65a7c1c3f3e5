//
//  StopSubscriptionRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 03.12.2019.
//  Copyright © 2019 GrowFood. All rights reserved.
//

import Foundation

final class StopSubscriptionRequest: Requestable {
    
    typealias ResponseType = CancelSubscriptionResponse
    
    private var subscriptionId: String
    
    init(subscriptionId: String) {
        self.subscriptionId = subscriptionId
    }
    
    var endpoint: String {
        return "subscriptions/stop"
    }
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        let params: [String : Any] = ["clientSubscriptionId_H": subscriptionId]
        return params
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
