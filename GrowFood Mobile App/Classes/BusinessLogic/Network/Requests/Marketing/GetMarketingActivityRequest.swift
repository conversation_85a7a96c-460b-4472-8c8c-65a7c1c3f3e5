//
//  GetMarketingActivityRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 21.08.2022.
//  Copyright © 2022 GrowFood. All rights reserved.
//

import Foundation

final class GetMarketingActivityRequest: Requestable {
    
    typealias ResponseType = GetMarketingActivityResponse
    
    var endpoint: String {
        return "marketing-activity"
    }
    
    var method: Network.Method = .get
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return nil
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
