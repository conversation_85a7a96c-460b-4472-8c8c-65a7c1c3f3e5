//
//  GetWidgetsFullRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 29.07.2023.
//  Copyright © 2023 GrowFood. All rights reserved.
//

import Foundation

struct GetWidgetsFullRequest: Requestable {
    typealias ResponseType = GetWidgetsFullResponse
    
    let endpoint = "widgets-full"
    
    let method: Network.Method = .get
    
    let query: Network.QueryType = .json
    
    var parameters: [String: Any]? { nil }
    
    var headers: [String: String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    let timeout : TimeInterval = 50
    
    var cachePolicy : NSURLRequest.CachePolicy {
        .reloadIgnoringLocalAndRemoteCacheData
    }
}

