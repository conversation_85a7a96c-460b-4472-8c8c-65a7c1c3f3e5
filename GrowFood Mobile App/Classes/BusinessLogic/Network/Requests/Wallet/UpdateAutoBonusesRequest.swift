//
//  UpdateAutoBonusesRequest.swift
//  GrowFood Mobile App
//
//  Created by Павел Аристов on 02.03.2021.
//  Copyright © 2021 GrowFood. All rights reserved.
//

import Foundation

final class UpdateAutoBonusesRequest: Requestable {
    
    typealias ResponseType = BaseProfileResponse
    
    private let isOn: Bool
    private let type: WalletService.AutoBonusesSettingsType
    
    init(type: WalletService.AutoBonusesSettingsType, isOn: Bool) {
        self.type = type
        self.isOn = isOn
    }
    
    var endpoint: String {
        return "clients/bonus-settings\(isOn ? "" : "/cancel")"
    }
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return [
            "type": type.rawValue
        ]
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}

