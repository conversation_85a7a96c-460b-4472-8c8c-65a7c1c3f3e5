//
//  ApplyCertificateRequest.swift
//  GrowFood Mobile App
//
//  Created by Павел Аристов on 08.04.2021.
//  Copyright © 2021 GrowFood. All rights reserved.
//

final class ApplyCertificateRequest: Requestable {
    
    typealias ResponseType = ApplyCertificateResponse
    
    var certificate: String
    
    init(certificate: String) {
        self.certificate = certificate
    }
    
    var endpoint: String {
        return "clients/apply-certificate"
    }
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return [
            "promocode": certificate
        ]
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 20
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
    
}
