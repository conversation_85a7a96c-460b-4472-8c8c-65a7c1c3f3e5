//
//  LongateWalletBonusRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 27.01.2021.
//  Copyright © 2021 GrowFood. All rights reserved.
//

import Foundation

final class LongateWalletBonusRequest: Requestable {
    
    typealias ResponseType = LongateWalletBonusResponse
    
    var endpoint: String {
        return "finance/longate-bonuses"
    }
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return nil
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
