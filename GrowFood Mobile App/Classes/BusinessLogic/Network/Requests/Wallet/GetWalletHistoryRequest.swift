//
//  GetWalletHistoryRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 22.07.2022.
//  Copyright © 2022 GrowFood. All rights reserved.
//

final class GetWalletHistoryRequest: Requestable {
    
    typealias ResponseType = GetWalletHistoryResponse
    
    private let page: Int?
    private let month: String?
    
    init(page: Int? = nil, month: String? = nil) {
        self.page = page
        self.month = month
    }
    
    var endpoint: String {
        if month != nil  {
            return "clients/wallet/history-by-month"
        }
        return "clients/wallet/history-total"
    }
    
    
    var method: Network.Method = .get
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        var param: [String : Any] = [:]
        param["brandId_H"] = "lY"
        
        if let page = page{
            param["page"] = page
        }
        
       if let month = month {
            param["month"] = month
        }
        
        return param
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}


