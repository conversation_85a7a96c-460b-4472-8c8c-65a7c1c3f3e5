//
//  GetWalletRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 27.01.2021.
//  Copyright © 2021 GrowFood. All rights reserved.
//

final class GetWalletRequest: Requestable {
    
    typealias ResponseType = GetWalletResponse
    
    var endpoint: String {
        return "clients/wallet"
    }
    
    var method: Network.Method = .get
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return nil
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
