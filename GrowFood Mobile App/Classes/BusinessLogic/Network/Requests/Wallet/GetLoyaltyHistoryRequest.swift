//
//  GetLoyaltyHistoryRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 01.02.2021.
//  Copyright © 2021 GrowFood. All rights reserved.
//

final class GetLoyaltyHistoryRequest: Requestable {
    
    typealias ResponseType = GetLoyaltyHistoryResponse
    
    private let page: Int
    
    init(page: Int) {
        self.page = page
    }
    
    var endpoint: String = "clients/loyalty/history"
    
    var method: Network.Method = .get
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return [
            "page": page
        ]
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}

