//
//  Untitled.swift
//  GrowFood Mobile App
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 17.04.2025.
//  Copyright © 2025 GrowFood. All rights reserved.
//

import Foundation

final class SetBonusSettingsChangeRequest: Requestable {
    
    typealias ResponseType = BaseSuccessResponse
    var bonusApplyLimit: Int
    
    init(bonusApplyLimit: Int) {
        self.bonusApplyLimit = bonusApplyLimit
    }
    
    var endpoint: String {
        return "clients/bonus-settings/change"
    }
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        var param: [String : Any] = [:]
        param["bonusApplyLimit"] = bonusApplyLimit
        return param
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
