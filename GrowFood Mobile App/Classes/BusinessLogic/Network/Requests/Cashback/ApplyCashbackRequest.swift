//
//  ApplyCashbackRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 24.12.2020.
//  Copyright © 2020 GrowFood. All rights reserved.
//

import Foundation

final class ApplyCashbackRequest: Requestable {
    
    typealias ResponseType = OrderCashbackResponse
    
    private let sum: Int
    private let orderIdH: String
    private let forDraftDebt: Bool
    private let applyAdditional: Bool /// Добавить бонусы без удаления предыдущих
    
    init(orderIdH: String, sum: Int, forDraftDebt: Bool = false, applyAdditional: Bool = false) {
        self.sum = sum
        self.orderIdH = orderIdH
        self.forDraftDebt = forDraftDebt
        self.applyAdditional = applyAdditional
    }
    
    var endpoint: String {
        return "orders/bonus/apply"
    }
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return [
            "orderId_H": orderIdH,
            "sum": sum,
            "forDraftDebt": forDraftDebt,
            "applyAdditional": applyAdditional
        ]
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
