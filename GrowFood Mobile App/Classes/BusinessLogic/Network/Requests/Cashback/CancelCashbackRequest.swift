//
//  CancelCashbackRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 24.12.2020.
//  Copyright © 2020 GrowFood. All rights reserved.
//

import Foundation

final class CancelCashbackRequest: Requestable {
    
    typealias ResponseType = OrderCashbackResponse
    
    private let orderIdH: String
    private let forDraftDebt: Bool
    
    init(orderIdH: String, forDraftDebt: Bool = false) {
        self.orderIdH = orderIdH
        self.forDraftDebt = forDraftDebt
    }
    
    var endpoint: String {
        return "orders/bonus/cancel"
    }
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return [
            "orderId_H": orderIdH,
            "forDraftDebt": forDraftDebt
        ]
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
