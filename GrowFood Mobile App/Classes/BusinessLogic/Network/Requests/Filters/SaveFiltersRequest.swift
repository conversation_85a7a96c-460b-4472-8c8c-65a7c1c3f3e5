//
//  SaveFiltersRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 01.09.2020.
//  Copyright © 2020 GrowFood. All rights reserved.
//

import Foundation

final class SaveFiltersRequest: Requestable {
    
    typealias ResponseType = SaveFiltersResponse
    
    var filters: [MenuFilterSettingViewModel]
    
    init(filters: [MenuFilterSettingViewModel]) {
        self.filters = filters
    }
    
    var endpoint: String {
        return "client-details/update-last-filter"
    }
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        var params: [String : Any] = [:]
        var filtersDict: [Int] = []
        filtersDict = filters.map { (filter) -> Int in
            return filter.id
        }
        params["lastFilterIds"] = filtersDict
        
        /// Если необходима миграция на новые фильтры
        if UserService.shared.isProposeFiltersChange {
            params["setProposedFiltersVersion"] = true
        }
        return params
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
