//
//  SetFilterToOrderRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 17.11.2020.
//  Copyright © 2020 GrowFood. All rights reserved.
//

import Foundation

final class SetFilterToOrderRequest: Requestable {
    
    typealias ResponseType = NewOrderResponse
    
    var filterIds: [Int]
    var orderIdH: String
    
    init(filterIds: [Int],
         orderIdH: String) {
        self.filterIds = filterIds
        self.orderIdH = orderIdH
    }
    
    var endpoint: String {
        return "orders/set-filter"
    }
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return ["orderId_H": orderIdH,
                "filterIds": filterIds]
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 20
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
    
}
