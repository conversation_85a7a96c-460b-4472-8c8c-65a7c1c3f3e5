//
//  UpdateMealsFilterRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 13.08.2021.
//  Copyright © 2021 GrowFood. All rights reserved.
//

import Foundation

struct UpdateMealsFilterRequest: Requestable {
    typealias ResponseType = BaseSuccessResponse
    
    let endpoint = "customization-info/filters/meals"
    
    let method: Network.Method = .post
    
    let query: Network.QueryType = .json
    
    var parameters: [String: Any]?
    
    var headers: [String: String]? {
        authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    let timeout : TimeInterval = 50
    
    var cachePolicy : NSURLRequest.CachePolicy = .reloadIgnoringLocalAndRemoteCacheData
    
    init(menuType: String?, templateIds: [Int]) {
        parameters = [Keys.templateIds.rawValue: templateIds]
        
        if let menuType = menuType {
            parameters?[Keys.menuType.rawValue] = menuType
        }
    }
}


private extension UpdateMealsFilterRequest {
    enum Keys: String {
        case menuType
        case templateIds
    }
}
