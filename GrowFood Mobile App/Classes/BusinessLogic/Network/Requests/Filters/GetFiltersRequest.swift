//
//  GetFiltersRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 02.08.2020.
//  Copyright © 2020 GrowFood. All rights reserved.
//

import Foundation

final class GetFiltersRequest: Requestable {
    
    typealias ResponseType = GetFiltersResponse
    
    
    init() {
    }
    
    var endpoint: String {
        return "customization-info/filters"
    }
    
    var method: Network.Method = .get
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        /// Если необходима миграция на новые фильтры
        if UserService.shared.isProposeFiltersChange {
            var params: [String : Any] = [:]
            params["getProposedVersion"] = 1
            return params
        }
        return nil
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
