//
//  UpdateDaysFilterRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 27.10.2021.
//  Copyright © 2021 GrowFood. All rights reserved.
//

import Foundation

struct UpdateDaysFilterRequest: Requestable {
    typealias ResponseType = BaseSuccessResponse
    
    let endpoint = "customization-info/filters/days"
    
    let method: Network.Method = .post
    
    let query: Network.QueryType = .json
    
    var parameters: [String: Any]?
    
    var headers: [String: String]? {
        authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    let timeout : TimeInterval = 50
    
    var cachePolicy : NSURLRequest.CachePolicy = .reloadIgnoringLocalAndRemoteCacheData
    
    init(menuType: String?, templateIds: [Int]) {
        parameters = [Keys.templateIds.rawValue: templateIds]
        
        if let menuType = menuType {
            parameters?[Keys.menuType.rawValue] = menuType
        }
    }
}


private extension UpdateDaysFilterRequest {
    enum Keys: String {
        case menuType
        case templateIds
    }
}
