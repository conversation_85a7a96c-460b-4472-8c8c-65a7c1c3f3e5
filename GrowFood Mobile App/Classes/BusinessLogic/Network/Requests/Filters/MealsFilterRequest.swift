struct MealsFilterRequest: Requestable {
    typealias ResponseType = MealsFilterResponse
    
    let endpoint = "customization-info/filters/meals"
    
    let method: Network.Method = .get
    
    let query: Network.QueryType = .json
    
    var parameters: [String: Any]?
    
    var headers: [String: String]? {
        authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    let timeout : TimeInterval = 50
    
    var cachePolicy : NSURLRequest.CachePolicy = .reloadIgnoringLocalAndRemoteCacheData
    
    init(menuType: String?) {
        
        if let menuType = menuType {
            parameters = [Keys.menuType.rawValue: menuType]
        }
    }
}


private extension MealsFilterRequest {
    enum Keys: String {
        case menuType
    }
}
