//
//  GetCustomMenuRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 05.06.2023.
//  Copyright © 2023 GrowFood. All rights reserved.
//

import Foundation
import DatadogTrace

struct CustomMenuRequest: Equatable {
    static func == (lhs: CustomMenuRequest, rhs: CustomMenuRequest) -> Bool {
        return lhs.cityId == rhs.cityId &&
        lhs.menuType == rhs.menuType &&
        lhs.firstDeliveryDate == rhs.firstDeliveryDate &&
        lhs.firstDeliverySession == rhs.firstDeliverySession &&
        lhs.menuDays == rhs.menuDays &&
        lhs.pricePlanId == rhs.pricePlanId &&
        lhs.abonementType == rhs.abonementType &&
        lhs.filters == rhs.filters &&
        lhs.mealNumbersToRemove == rhs.mealNumbersToRemove &&
        lhs.drinksAdd == rhs.drinksAdd
    }
    
    var showPacksDuplicates: Bool
    var cityId: Int
    var menuType: String
    var firstDeliveryDate: Date
    var firstDeliverySession: String
    var menuDays: Int
    var pricePlanId: Int
    var abonementType: String
    var filters: [MenuFilterSettingViewModel]
    var mealNumbersToRemove: [Int]?
    var drinksAdd: Bool?
    
    var planDeliveryDate: Date {
        
        return firstDeliverySession == "evening" ? firstDeliveryDate : firstDeliveryDate.adjust(.day, offset: -1)
    }
    
    var filtersHash: String {
        let hash = filters.sorted { left, right in
            left.id > right.id
        }.compactMap { String($0.id) }.joined()
        return hash
    }
    
    init(cityId: Int,
         menuType: String,
         firstDeliveryDate: Date,
         firstDeliverySession: String,
         menuDays: Int,
         abonementType: String,
         pricePlanId: Int,
         filters: [MenuFilterSettingViewModel],
         mealNumbersToRemove: [Int]?,
         showPacksDuplicates: Bool,
         drinksAdd: Bool? = nil) {
        if firstDeliveryDate.compare(.isSameDay(as: DateFormatter.yyyyMMdd.date(from: "2025-03-12")!)) {
            print("alarm")
        }
        self.drinksAdd = drinksAdd
        self.cityId = cityId
        self.menuType = menuType
        self.firstDeliveryDate = firstDeliveryDate
        self.firstDeliverySession = firstDeliverySession
        self.menuDays = menuDays
        self.abonementType = abonementType
        self.filters = filters
        self.pricePlanId = pricePlanId
        self.showPacksDuplicates = showPacksDuplicates
        self.mealNumbersToRemove = mealNumbersToRemove
    }
    
    var params: [String: Any] {
        var plannedDeliveryDate = firstDeliveryDate
        if firstDeliverySession == "morning" {
            plannedDeliveryDate = firstDeliveryDate.adjust(.day, offset: -1)
        }
        var params = ["cityId": cityId,
                      "menuType": menuType,
                      "plannedDeliveryDate": DateFormatter.yyyyMMdd.string(from: plannedDeliveryDate),
                      "daysCount": menuDays,
                      "pricePlanId": pricePlanId,
                      "showDenormalizedCustomConfig": true] as [String : Any]
        
        if abonementType != "" {
            params["abonementType"] = abonementType
        }
        
        if filters.count > 0 {
            var filtersDict: [Int] = []
            filtersDict = filters.map { (filter) -> Int in
                return filter.id
            }
            params["filterIds"] = filtersDict
        }
                
        return params
    }
}

final class GetCustomMenuRequest: Requestable {
    
    typealias ResponseType = GetCustomMenuResponse
    
    var requestData: CustomMenuRequest?
    let parentSpan: OTSpan?
    
    init(requestData: CustomMenuRequest,
         parentSpan: OTSpan?) {
        print("parent span: \(parentSpan != nil ? "ima" : "nema")")
        if parentSpan == nil {
            print(PerformanceTracker.datadogTraces)
        }
        self.parentSpan = parentSpan
        self.requestData = requestData
    }
    
    //Для KMP
    init(params: [String : Any],
         parentSpan: OTSpan?) {
        print("parent span: \(parentSpan != nil ? "ima" : "nema")")
        if parentSpan == nil {
            print(PerformanceTracker.datadogTraces)
        }
        self.params = params
        self.parentSpan = parentSpan
    }
    
    var endpoint: String {
        if UserService.shared.isAuthorized {
            return "customization-info/custom-menu"
        }
        else {
            return "customization-info/custom-menu-no-auth"
        }
    }
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var params: [String : Any]?
    var parameters: [String : Any]? {
        if let params {
            return params
        }
        
        if requestData?.menuDays == 0 {
            print("ЧТО-ТО НЕ ТАК")
        }
        return requestData?.params ?? [:]
    }
    
    var headers: [String : String]? {
        var headers = UserService.shared.isAuthorized ? authHeaders : notAuthHeaders
        if let parentSpan {
            let networkSpan = Tracer.shared().startSpan(operationName: "load menu", childOf: parentSpan.context)
            let headersWriter = HTTPHeadersWriter.init(samplingStrategy: .headBased, traceContextInjection: .all)
            Tracer.shared().inject(spanContext: networkSpan.context, writer: headersWriter)
            for (headerField, value) in headersWriter.traceHeaderFields {
                headers[headerField] = value
            }
        }
        else {
            print("wrong")
        }
        return headers
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 60
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
