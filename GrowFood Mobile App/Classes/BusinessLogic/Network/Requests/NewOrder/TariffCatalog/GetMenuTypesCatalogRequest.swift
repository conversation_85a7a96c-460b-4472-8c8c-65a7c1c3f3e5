//
//  GetMenuTypesCatelogRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 31.10.2022.
//  Copyright © 2022 GrowFood. All rights reserved.
//

import Foundation

final class GetMenuTypesCatalogRequest: Requestable {
    
    typealias ResponseType = GetMenuTypesCatalogResponse
    
    let cityId: Int
    let clientAddressIdH: String?
    
    init(cityId: Int,
         clientAddressIdH: String?) {
        self.cityId = cityId
        self.clientAddressIdH = clientAddressIdH
    }
    
    var endpoint: String {
        return "menu/catalog"
    }
    
    var method: Network.Method = .get
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        var params: [String : Any] = ["cityId": cityId]
        if let clientAddressIdH = clientAddressIdH {
            params["clientAddressId_H"] = clientAddressIdH
        }
        return params
    }
    
    var headers: [String : String]? {
        return UserService.shared.isAuthorized ? authHeaders : notAuthHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 60
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
