//
//  GetTariffCategoriesRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 31.10.2022.
//  Copyright © 2022 GrowFood. All rights reserved.
//

import Foundation

final class GetTariffCategoriesRequest: Requestable {
    
    typealias ResponseType = GetTariffSectionsResponse
    
    var endpoint: String {
        return "libs/menu-types-catalog-config"
    }
    
    var method: Network.Method = .get
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return nil
    }
    
    var headers: [String : String]? {
        return UserService.shared.isAuthorized ? authHeaders : notAuthHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 60
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
