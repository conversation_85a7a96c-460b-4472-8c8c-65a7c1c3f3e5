//
//  GetTariffsRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 30.09.2024.
//  Copyright © 2024 GrowFood. All rights reserved.
//

final class GetTariffsRequest: Requestable {
    
    typealias ResponseType = GetTariffsResponse
    
    init() {
    }
    
    var endpoint: String {
        return "menu/catalog-config"
    }
    
    var method: Network.Method = .get
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return nil
    }
    
    var headers: [String : String]? {
        let headers = UserService.shared.isAuthorized ? authHeaders : notAuthHeaders
        print(headers)
        return headers
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 60
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
