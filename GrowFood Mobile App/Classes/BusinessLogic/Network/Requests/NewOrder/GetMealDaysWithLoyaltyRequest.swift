//
//  GetMealDaysWithLoyaltyRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 28.09.2023.
//  Copyright © 2023 GrowFood. All rights reserved.
//

import Foundation
import DatadogTrace

final class GetMealDaysWithLoyaltyRequest: Requestable {
    
    typealias ResponseType = GetMealDaysWithLoyaltyResponse
    
    let cityId: Int
    let isFamily: Bool
    let useClientsBmi: Bool
    let menuTypeId: Int
    let firstDeliveryDate: Date?
    let session: String?
    let pricePlanId: Int
    let parentSpan: OTSpan?
    
    init(with params: MealDaysService.MealDaysParams,
         parentSpan: OTSpan?) {
        self.parentSpan = parentSpan
        self.cityId = params.cityId
        self.isFamily = params.isFamily
        self.useClientsBmi = params.useClientsBmi
        self.firstDeliveryDate = params.firstDeliveryDate
        self.session = params.firstDeliverySession
        self.menuTypeId = params.menuTypeId
        self.pricePlanId = params.pricePlanId
    }
    
    var endpoint: String {
        if UserService.shared.isAuthorized {
            return "menu/meal-days-with-loyalty-benefits"
        }
        else {
            return "menu/meal-days-with-loyalty-benefits-no-auth"
        }
    }
    
    var method: Network.Method = .get
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        var params: [String : Any] = ["cityId": cityId,
                                      "family": isFamily ? 1 : 0,
                                      "menuTypeId": menuTypeId,
                                      "useClientsBmi": useClientsBmi ? 1 : 0,
                                      "pricePlanId": pricePlanId]
        if firstDeliveryDate != nil && session != nil {
            let dateFormatter = DateFormatter.yyyyMMdd
            params["firstDeliveryDate"] = dateFormatter.string(from: firstDeliveryDate!)
            params["session"] = session!
        }
        
        return params
    }
    
    var headers: [String : String]? {
        var headers = UserService.shared.isAuthorized ? authHeaders : notAuthHeaders
        if let parentSpan {
            let networkSpan = Tracer.shared().startSpan(operationName: "load mealdays", childOf: parentSpan.context)
            let headersWriter = HTTPHeadersWriter.init(samplingStrategy: .headBased, traceContextInjection: .all)
            Tracer.shared().inject(spanContext: networkSpan.context, writer: headersWriter)
            for (headerField, value) in headersWriter.traceHeaderFields {
                headers[headerField] = value
            }
        }
        return headers
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 60
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
