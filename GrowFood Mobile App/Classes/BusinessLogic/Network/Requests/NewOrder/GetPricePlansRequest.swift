//
//  GetPricePlansRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 11.07.2023.
//  Copyright © 2023 GrowFood. All rights reserved.
//

import Foundation

final class GetPricePlansRequest: Requestable {
    
    typealias ResponseType = GetPricePlansReponse
    
    let menuType: String
    let cityId: Int
    
    init(menuType: String,
         cityId: Int) {
        self.menuType = menuType
        self.cityId = cityId
    }
    
    var endpoint: String {
        return "libs/price-plans-by-templates"
    }
    
    var method: Network.Method = .get
    
    var query: Network.QueryType = .path
    
    var parameters: [String : Any]? {
        return ["menuType": menuType,
                "cityId": cityId]
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
