//
//  GetPackCategoriesRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 02.06.2022.
//  Copyright © 2022 GrowFood. All rights reserved.
//

import Foundation

final class GetPackCategoriesRequest: Requestable {
    
    typealias ResponseType = GetPackCategoriesResponse
    
    
    init() {}
    
    var endpoint: String {
        return "libs/pack-filter-categories"
    }
    
    var method: Network.Method = .get
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        nil
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
