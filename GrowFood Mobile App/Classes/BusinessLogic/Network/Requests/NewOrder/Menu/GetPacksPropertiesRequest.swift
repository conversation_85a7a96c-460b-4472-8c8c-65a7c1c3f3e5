//
//  GetPacksPropertiesRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 20.09.2021.
//  Copyright © 2021 GrowFood. All rights reserved.
//

import Foundation
import DatadogTrace

final class GetPacksPropertiesRequest: Requestable {
    
    typealias ResponseType = PackPropertiesResponse
    
    
    init() {}
    
    var endpoint: String {
        return "libs/packs-properties"
    }
    
    var method: Network.Method = .get
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        nil
    }
    
    var headers: [String : String]? {
        var headers = authHeaders
        let parentSpan = PerformanceTracker.datadogTraces["newOrderMenuLoading"]
        if let parentSpan {
            let networkSpan = Tracer.shared().startSpan(operationName: "load packsProperties", childOf: parentSpan.context)
            let headersWriter = HTTPHeadersWriter.init(samplingStrategy: .headBased, traceContextInjection: .all)
            Tracer.shared().inject(spanContext: networkSpan.context, writer: headersWriter)
            for (headerField, value) in headersWriter.traceHeaderFields {
                headers[headerField] = value
            }
        }
        return headers
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
