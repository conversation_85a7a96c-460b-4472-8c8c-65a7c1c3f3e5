//
//  AttachMemberToOrderRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 18.06.2020.
//  Copyright © 2020 GrowFood. All rights reserved.
//

import Foundation

final class AttachMemberToOrderRequest: Requestable {
    
    typealias ResponseType = NewOrderResponse
    
    var relationName: String
    var orderIdH: String
    
    init(orderId: String,
         relationName: String) {
        self.relationName = relationName
        self.orderIdH = orderId
    }
    
    var endpoint: String {
        return "family/attach-member-to-order"
    }
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return ["orderId_H": orderIdH,
                "familyMember": ["relationName": relationName]]
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
