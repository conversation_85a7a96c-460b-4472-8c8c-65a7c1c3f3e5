//
//  DeleteFamilyMemberRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 15.06.2020.
//  Copyright © 2020 GrowFood. All rights reserved.
//

import Foundation

final class DeleteFamilyMemberRequest: Requestable {
    
    typealias ResponseType = DeleteFamilyMemberResponse
    
    var idH: String
    
    init(idH: String) {
        self.idH = idH
    }
    
    var endpoint: String {
        return "family/members"
    }
    
    var method: Network.Method = .delete
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return ["familyMemberId_H": idH]
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
