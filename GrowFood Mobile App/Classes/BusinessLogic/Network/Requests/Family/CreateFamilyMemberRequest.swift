//
//  CreateFamilyMemberRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 20.05.2020.
//  Copyright © 2020 GrowFood. All rights reserved.
//

import Foundation

final class CreateFamilyMemberRequest: Requestable {
    
    typealias ResponseType = CreateFamilyMemberResponse
    
    var relationName: String
    
    init(relationName: String) {
        self.relationName = relationName
    }
    
    var endpoint: String {
        return "family/members"
    }
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return ["relationName": relationName]
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
