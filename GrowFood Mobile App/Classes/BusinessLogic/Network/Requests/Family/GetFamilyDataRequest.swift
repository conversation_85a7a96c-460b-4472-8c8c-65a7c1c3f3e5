//
//  GetFamilyDataRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 15.05.2020.
//  Copyright © 2020 GrowFood. All rights reserved.
//

import Foundation

final class GetFamilyDataRequest: Requestable {
    
    typealias ResponseType = GetFamilyDataResponse
    
    
    init() {
    }
    
    var endpoint: String {
        return "family/members"
    }
    
    var method: Network.Method = .get
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return nil
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
