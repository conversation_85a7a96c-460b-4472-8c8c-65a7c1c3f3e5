//
//  GetNPSFinalScreenRequest.swift
//  GrowFood Mobile App
//
//  Created by Паве<PERSON> Аристов on 22.11.2021.
//  Copyright © 2021 GrowFood. All rights reserved.
//

final class GetNPSFinalScreenRequest: Requestable {
    
    typealias ResponseType = GetNPSFinalScreenResponse
    
    private let sessionId: Int
    
    init(sessionId: Int) {
        self.sessionId = sessionId
    }
    
    var endpoint: String {
        return "questionnaire/final-screen"
    }
    
    var method: Network.Method = .get
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return [
            "sessionId": sessionId
        ]
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
