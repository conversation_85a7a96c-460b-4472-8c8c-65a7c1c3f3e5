//
//  SetNPSNewStatusRequest.swift
//  GrowFood Mobile App
//
//  Created by Павел Аристов on 22.11.2021.
//  Copyright © 2021 GrowFood. All rights reserved.
//

extension SetNPSNewStatusRequest {
    enum StatusType {
        case shown, invited, closed
        
        var key: String {
            switch self {
            case .shown: return "was_shown"
            case .invited: return "was_invited"
            case .closed: return "was_closed"
            }
        }
    }
}

final class SetNPSNewStatusRequest: Requestable {
    
    typealias ResponseType = BaseSuccessResponse
    
    private let type: StatusType
    private let sessionId: Int
    
    init(type: StatusType, sessionId: Int) {
        self.type = type
        self.sessionId = sessionId
    }
    
    var endpoint: String {
        return "questionnaire/update-status"
    }
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return [
            "type": type.key,
            "sessionId": sessionId,
            "value": true
        ]
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
