//
//  NPSSaveCommunicationMethodRequest.swift
//  GrowFood Mobile App
//
//  Created by Паве<PERSON> Аристов on 08.12.2021.
//  Copyright © 2021 GrowFood. All rights reserved.
//

final class NPSSaveCommunicationMethodRequest: AttachmentRequestable {
    
    typealias ResponseType = BaseSuccessResponse
    
    private let sessionId: Int
    private let communicationMethod: NPSCommunicationMethod?

    init(sessionId: Int, communicationMethod: NPSCommunicationMethod?) {
        self.sessionId = sessionId
        self.communicationMethod = communicationMethod
    }
    
    var endpoint: String {
        return "questionnaire/save-communication-method"
    }
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        
        var parameters: [String: Any] = [
            "sessionId": sessionId
        ]
        
        if let method = communicationMethod {
            parameters["type"] = method.requestParameter
        }
        
        return parameters
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
