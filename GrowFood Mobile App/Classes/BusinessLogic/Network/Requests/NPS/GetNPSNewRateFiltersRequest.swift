//
//  GetNPSNewRateFiltersRequest.swift
//  GrowFood Mobile App
//
//  Created by Павел Аристов on 22.11.2021.
//  Copyright © 2021 GrowFood. All rights reserved.
//

final class GetNPSNewRateFiltersRequest: Requestable {
    
    typealias ResponseType = GetNPSNewRateFiltersResponse
    
    var endpoint: String {
        return "questionnaire/new/rate-filters"
    }
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return nil
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
