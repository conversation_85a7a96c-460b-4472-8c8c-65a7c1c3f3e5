//
//  NPSSaveAnswerRequest.swift
//  GrowFood Mobile App
//
//  Created by Павел Аристов on 22.11.2021.
//  Copyright © 2021 GrowFood. All rights reserved.
//

final class NPSSaveAnswerRequest: AttachmentRequestable {
    
    typealias ResponseType = BaseSuccessResponse
    
    private let saveAnswerModel: NPSSaveAnswerModel

    init(_ saveAnswerModel: NPSSaveAnswerModel) {
        self.saveAnswerModel = saveAnswerModel
    }
    
    var endpoint: String {
        return "questionnaire/save-answer"
    }
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return saveAnswerModel.parameters
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
