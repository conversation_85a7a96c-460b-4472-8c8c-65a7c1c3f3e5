//
//  SendDishFeedbackRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 28/11/2018.
//  Copyright © 2018 GrowFood. All rights reserved.
//

import Foundation
import UIKit

final class DishFeedbackData {
    var taskId: Int = 0
    var rateIds: [Int] = [Int]()
    var comment: String? = nil
    var images: [UIImage] = [UIImage]()
    
    var params: [String: Any] {
        var params: [String: Any] = ["rateIds":rateIds]
        
        if let cmt = comment {
            params["comment"] = cmt
        }
        return params
    }
    
    init(taskId: Int,
         ids: [Int],
         comment: String? = nil,
         images: [UIImage]? = nil) {
        self.taskId = taskId
        self.rateIds = ids
        self.comment = comment
        if images != nil {
            self.images = images!
        }
    }
}


final class SendDishFeedbackRequest: Requestable {
    
    typealias ResponseType = GetFeedbackTasksResponse
    
    var feedbackData: DishFeedbackData!
    
    init(feedbackData: DishFeedbackData) {
        self.feedbackData = feedbackData
    }
    
    var endpoint: String {
        return "rating-tasks/rate/\(feedbackData.taskId)"
    }
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return feedbackData.params
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 20
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
