//
//  SendDriverFeedbackRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 26/11/2018.
//  Copyright © 2018 GrowFood. All rights reserved.
//

import Foundation

final class DriverFeedbackData {
    var stars: Int = 0
    var taskId: Int = 0
    var rateIds: [Int]? = nil
    var comment: String? = nil
    var contactWithMe: Bool? = nil
    
    var params: [String: Any] {
        var params: [String: Any] = ["stars": stars]
        if let ids = rateIds {
            params["rateIds"] = ids
        }
        if let cmt = comment {
            params["comment"] = cmt
        }
        if let contactWithMe = contactWithMe {
            params["needFeedback"] = contactWithMe
        }
        
        return params
    }
    
    init(rating: Int,
         taskId: Int,
         ids: [Int]? = nil,
         comment: String? = nil,
         contactWithMe: Bool? = nil) {
        self.taskId = taskId
        self.stars = rating
        self.rateIds = ids
        self.comment = comment
        self.contactWithMe = contactWithMe
    }
}

final class SendDriverFeedbackRequest: Requestable {
    
    typealias ResponseType = GetFeedbackTasksResponse
    
    var feedbackData: DriverFeedbackData!
    
    init(feedbackData: DriverFeedbackData) {
        self.feedbackData = feedbackData
    }
    
    var endpoint: String {
        return "rating-tasks/rate-driver/\(feedbackData.taskId)"
    }
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return feedbackData.params
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 20
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}


final class SendDriverEmptyCloseRequest: Requestable {
    
    typealias ResponseType = GetFeedbackTasksResponse
    
    private let taskId: Int
    
    init(taskId: Int) {
        self.taskId = taskId
    }
    
    var endpoint: String {
        return "rating-tasks/close/\(taskId)"
    }
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return nil
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 20
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
