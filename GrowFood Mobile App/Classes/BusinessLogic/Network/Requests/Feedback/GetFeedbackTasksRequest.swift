//
//  GetFeedbackTasksRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 21/11/2018.
//  Copyright © 2018 GrowFood. All rights reserved.
//

import Foundation

final class GetFeedbackTasksRequest: Requestable {
    
    typealias ResponseType = GetFeedbackTasksResponse
    
    var endpoint: String {
        return "rating-tasks"
    }
    
    var method: Network.Method = .get
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return nil
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 60
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}

final class GetDriverTipsRequest: Requestable {
    
    typealias ResponseType = GetDriverTipsResponse
    
    var endpoint: String {
        return "tips/templates"
    }
    
    var method: Network.Method = .get
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return nil
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 60
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}

final class AddDriverTipsTemplateRequest: Requestable {
    
    typealias ResponseType = GetDriverTipsResponse
    
    private let cardId: String?
    private let tipsVariantId: Int
    
    init(tipsVariantId: Int, cardId: String? = nil) {
        self.tipsVariantId = tipsVariantId
        self.cardId = cardId
    }
    
    var endpoint: String {
        return "tips/templates"
    }
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        
        var params: [String: Any] = [
            "amount_id": tipsVariantId
        ]
        
        if let cardId = cardId {
            params["external_client_card_H"] = cardId
        }
        
        return params
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 20
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}

final class RemoveDriverTipsTemplateRequest: Requestable {
    
    typealias ResponseType = GetDriverTipsResponse
    
    private let templateId: Int
    
    init(templateId: Int) {
        self.templateId = templateId
    }
    
    var endpoint: String {
        return "tips/templates/\(templateId)"
    }
    
    var method: Network.Method = .delete
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return nil
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 20
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
