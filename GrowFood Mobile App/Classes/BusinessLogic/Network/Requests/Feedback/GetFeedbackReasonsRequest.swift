//
//  GetFeedbackReasonsRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 21/11/2018.
//  Copyright © 2018 GrowFood. All rights reserved.
//

import Foundation

final class GetFeedbackReasonsRequest: Requestable {
    
    typealias ResponseType = GetFeedbackReasonsResponse
    
    var endpoint: String {
        return "feedback_data.json"
    }
    
    var method: Network.Method = .get
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return nil
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return storageBaseUrl
    }
    
    var timeout: TimeInterval {
        return 60
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
