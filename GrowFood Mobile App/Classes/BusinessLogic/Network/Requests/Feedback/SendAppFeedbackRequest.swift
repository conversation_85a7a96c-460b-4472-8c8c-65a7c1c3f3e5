//
//  SendAppFeedbackRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 04/07/2019.
//  Copyright © 2019 GrowFood. All rights reserved.
//

import Foundation
import UIKit

final class AppFeedbackData {
    var email: String = ""
    var message: String = ""
    var image: UIImage? = nil
    
    var params: [String: Any] {
        let params: [String: Any] = ["email": email,
                                     "comment": message]
//        if image != nil {
//            params["image"] = image!.jpegData(compressionQuality: 0.5)
//        }
        return params
    }
    
    init(email: String,
         message: String,
         image: UIImage?) {
        
        self.email = email
        self.message = message
        self.image = image
        
    }
}

final class SendAppFeedbackRequest: Requestable {
    
    typealias ResponseType = GetFeedbackTasksResponse
    
    var feedbackData: AppFeedbackData!
    
    init(feedbackData: AppFeedbackData) {
        self.feedbackData = feedbackData
    }
    
    var endpoint: String {
        return "feedback"
    }
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return feedbackData.params
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 20
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
