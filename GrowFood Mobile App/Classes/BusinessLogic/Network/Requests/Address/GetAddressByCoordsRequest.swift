//
//  GetAddressByCoordsRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 26/09/2018.
//  Copyright © 2018 GrowFood. All rights reserved.
//

import Foundation

final class GetAddressByCoordsRequest: Requestable {
    
    typealias ResponseType = GetAddressByCoordsResponse
    
    var lat: Double
    var lng: Double
    
    init(lat: Double,
         lng: Double) {
        self.lat = lat
        self.lng = lng
    }
    
    var endpoint: String {
        return "address/check-reverse"
    }
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return ["latitude": lat,
                "longitude": lng]
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 20
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .useProtocolCachePolicy
    }
}
