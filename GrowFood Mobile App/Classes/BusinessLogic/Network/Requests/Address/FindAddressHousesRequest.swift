//
//  FindAddressHousesRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 11.10.2022.
//  Copyright © 2022 GrowFood. All rights reserved.
//

import Foundation

final class FindAddressHousesRequest: Requestable {
    
    typealias ResponseType = FindAddressesResponse
    
    var searchData: SearchAddressData
    
    init(searchData: SearchAddressData) {
        self.searchData = searchData
    }
    
    var endpoint: String {
        return "address-suggest/houses"
    }
    
    var method: Network.Method = .get
    
    var query: Network.QueryType = .path
    
    var parameters: [String : Any]? {
        return searchData.housesParams
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 20
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .useProtocolCachePolicy
    }
}
