//
//  DeleteClientAddressRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 20/11/2018.
//  Copyright © 2018 GrowFood. All rights reserved.
//

import Foundation

final class DeleteClientAddressRequest: Requestable {
    
    typealias ResponseType = GetClientAddressesResponse
    
    private var addressId: String
    
    init(addressId: String) {
        self.addressId = addressId
    }
    
    var endpoint: String {
        return "clients/addresses"
    }
    
    var method: Network.Method = .delete
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        let params: [String : Any] = ["id_H": addressId]
        return params
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 20
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
