//
//  NewEditClientAddressRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 20/11/2018.
//  Copyright © 2018 GrowFood. All rights reserved.
//

import Foundation

final class NewEditClientAddressRequest: Requestable {
    
    typealias ResponseType = GetClientAddressesResponse
    
    private var addressData: AddressResponse
    private var addressId: String?
    
    init(addressData: AddressResponse, addressId: String?) {
        self.addressData = addressData
        self.addressId = addressId
    }
    
    var endpoint: String {
        return "clients/addresses"
    }
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        var params: [String : Any] = ["addressInfo": addressData.params]
        if let addressId {
            params["clientAddressId_H"] = addressId
        }
        return params
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 20
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
