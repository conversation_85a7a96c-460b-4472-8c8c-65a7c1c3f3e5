//
//  EditAddressFieldsRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 18/12/2018.
//  Copyright © 2018 GrowFood. All rights reserved.
//

import Foundation

final class EditAddressFieldsRequest: Requestable {
    
    typealias ResponseType = GetClientAddressesResponse
    
    private var addressId: String
    private var flat: String
    private var floor: String
    private var entrance: String
    private var hasNoFlat: Bool
    
    init(flat: String,
         floor: String,
         entrance: String,
         addressId: String,
         hasNoFlat: Bool) {
        self.flat = flat
        self.floor = floor
        self.entrance = entrance
        self.addressId = addressId
        self.hasNoFlat = hasNoFlat
    }
    
    var endpoint: String {
        return "clients/addresses/fields"
    }
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        var params: [String : Any] = ["clientAddressId_H": addressId]
        
        if floor != "" {
            params["floor"] = Int(floor)
        }
        if entrance != "" {
            params["entrance"] = entrance
        }
        params["hasNoFlat"] = hasNoFlat
        if !hasNoFlat {
            if flat != "" {
                params["flat"] = flat
            }
        }
        return params
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 20
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
