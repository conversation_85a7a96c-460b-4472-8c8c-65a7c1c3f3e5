//
//  CheckAddressRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 25/09/2018.
//  Copyright © 2018 GrowFood. All rights reserved.
//

import Foundation

final class CheckAddressRequest: Requestable {
    
    typealias ResponseType = GetAddressByCoordsResponse
    
    var searchData: SearchAddressData
    
    init(searchData: SearchAddressData) {
        self.searchData = searchData
    }
    
    var endpoint: String {
        return "deliveries/address/check"
    }
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .path
    
    var parameters: [String : Any]? {
        return searchData.checkParams
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
