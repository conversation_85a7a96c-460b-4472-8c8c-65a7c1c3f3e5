//
//  SaveUnavailableAddressRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 17.10.2022.
//  Copyright © 2022 GrowFood. All rights reserved.
//

import Foundation

final class SaveUnavailableAddressRequest: Requestable {
    
    typealias ResponseType = GetClientAddressesResponse
    
    private var addressData: AddressResponse
    
    init(addressData: AddressResponse) {
        self.addressData = addressData
    }
    
    var endpoint: String {
        return "clients/addresses/unavailable"
    }
    
    var method: Network.Method = .put
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        let params: [String : Any] = ["addressInfo": addressData.unavailableParams]
        return params
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 20
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
