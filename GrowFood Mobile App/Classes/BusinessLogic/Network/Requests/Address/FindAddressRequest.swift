//
//  FindAddressRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 25/09/2018.
//  Copyright © 2018 GrowFood. All rights reserved.
//

import Foundation

struct SearchAddressData {
    
    var kladrId: String
    var query: String
    var singleMode: Int
    
    
    var params: [String: Any] {
        let params: [String: Any] =  ["regionKladrId": kladrId,
                                      "query": query,
                                      "singleMode": singleMode]
        return params
    }
    
    var housesParams: [String: Any] {
        let params: [String: Any] =  ["regionKladrId": kladrId,
                                      "query": query]
        return params
    }
    
    var checkParams: [String: Any] {
        let params: [String: Any] =  ["regionKladrId": kladrId,
                                      "value": query]
        return params
    }
    
    init(kladrId: String,
         query: String,
         singleMode: Bool = false) {
        self.kladrId = kladrId
        self.query = query
        self.singleMode = singleMode ? 1 : 0
    }
}


final class FindAddressRequest: Requestable {
    
    typealias ResponseType = FindAddressesResponse
    
    var searchData: SearchAddressData
    
    init(searchData: SearchAddressData) {
        self.searchData = searchData
    }
    
    var endpoint: String {
        return "address-suggest"
    }
    
    var method: Network.Method = .get
    
    var query: Network.QueryType = .path
    
    var parameters: [String : Any]? {
        return searchData.params
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 20
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .useProtocolCachePolicy
    }
}
