//
//  CheckGFSbpStatusRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 22.11.2023.
//  Copyright © 2023 GrowFood. All rights reserved.
//

import Foundation

final class CheckGFSbpStatusRequest: Requestable {
    
    typealias ResponseType = ChargeSbpResponse
    
    private let paymentToken: String
    
    init(paymentToken: String) {
        self.paymentToken = paymentToken
    }
    
    var endpoint: String {
        return "payments/check-sbp-status"
    }
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return ["payment": paymentToken]
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
