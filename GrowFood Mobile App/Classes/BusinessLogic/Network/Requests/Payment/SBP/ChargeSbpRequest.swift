//
//  ChargeSbpRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 20.11.2023.
//  Copyright © 2023 GrowFood. All rights reserved.
//

import Foundation

final class ChargeSbpRequest: Requestable {
    
    typealias ResponseType = ChargeSbpResponse
    
    private var paymentToken: String
    private var saveCard: Bool
    
    init(paymentToken: String,
         saveCard: Bool) {
        self.paymentToken = paymentToken
        self.saveCard = saveCard
    }
    
    var endpoint: String {
        return "payments/charge-sbp"
    }
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        let params = ["payment": paymentToken,
                      "redirectUrl": "gf://payment/success",
                      "saveCard": saveCard] as? [String : Any]
        return params
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
