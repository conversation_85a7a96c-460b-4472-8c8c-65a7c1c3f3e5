//
//  SetPaymentDataRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 01.02.2024.
//  Copyright © 2024 GrowFood. All rights reserved.
//

import Foundation

final class SetPaymentDataRequest: Requestable {
    
    typealias ResponseType = CheckSbpStatusResponse
    
    private let payment: String
    private let bankIdH: String
    
    init(payment: String,
         bankIdH: String) {
        self.payment = payment
        self.bankIdH = bankIdH
    }
    
    var endpoint: String {
        return "payments/set-payment-data"
    }
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return ["payment": payment,
                "bankId_H": bankIdH]
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 120
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
