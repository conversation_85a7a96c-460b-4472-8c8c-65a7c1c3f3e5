//
//  CheckSbpStatusRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 22.11.2023.
//  Copyright © 2023 GrowFood. All rights reserved.
//

import Foundation

final class CheckSbpStatusRequest: Requestable {
    
    typealias ResponseType = CheckSbpStatusResponse
    
    private let publicId: String
    private let transactionId: String
    
    init(publicId: String,
         transactionId: String) {
        self.publicId = publicId
        self.transactionId = transactionId
    }
    
    var endpoint: String {
        return "payments/qr/status/wait"
    }
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return ["PublicId": publicId,
                "TransactionId": transactionId]
    }
    
    var headers: [String : String]? {
        return ["Content-Type": "application/json"]
    }
    
    var baseUrl: URL {
        return URL(string: "https://api.cloudpayments.ru")!
    }
    
    var timeout: TimeInterval {
        return 120
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
