//
//  PaymentInfoRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 01.07.2020.
//  Copyright © 2020 GrowFood. All rights reserved.
//

import Foundation

final class PaymentInfoRequest: Requestable {
    
    typealias ResponseType = PaymentInfoDataResponse
    
    private var paymentTargetType: PaymentTargetType
    private var externalService: String?
    
    init(paymentTargetType: PaymentTargetType,
         externalService: String?) {
        self.paymentTargetType = paymentTargetType
        self.externalService = externalService
    }
    
    var endpoint: String {
        switch paymentTargetType {
        case .order:
            return "payments/for-order"
        case .tips:
            return "payments/for-tip"
        }
    }
    
    var parameters: [String : Any]? {
        var params: [String : Any] = [:]
        switch paymentTargetType {
        case .order(_, orderIds_HU: let orderIds_HUs, _, _):
            params["orderIds_HU"] = orderIds_HUs
        case .tips(ratingTaskId: let ratingTaskId,
                   tipAmountId: let tipAmountId):
            params["ratingTaskId"] = ratingTaskId
            params["tipAmountId"] = tipAmountId
        }
        if let externalService = self.externalService {
            params["externalService"] = externalService
        }
        return params
    }
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
