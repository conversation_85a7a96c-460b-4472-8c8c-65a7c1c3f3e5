//
//  ChargeCardRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 13/09/2018.
//  Copyright © 2018 GrowFood. All rights reserved.
//

import Foundation


struct ChargeCardData {
    let cryptogram: String
    let paymentToken: String
    let name: String
    var redirectAfter3dsTo: String = "https://growfood.pro/mob-payment-redirect/"
    
    init(cryptogram: String,
         paymentToken: String,
         name: String) {
        self.cryptogram = cryptogram
        self.paymentToken = paymentToken
        self.name = name
        let redirectString = UserService.shared.curUserSettings?.userConfig?.payment3dsRedirectUrl ?? ""
        if redirectString != "" {
            redirectAfter3dsTo = redirectString
        }
    }
    
    var params: [String: Any] {
        let params: [String: Any] = ["cryptogram": cryptogram,
                                     "payment": paymentToken,
                                     "name": name,
                                     "redirectAfter3dsTo": redirectAfter3dsTo]
        return params
    }
    
    
}


final class ChargeCardRequest: Requestable {
    
    typealias ResponseType = ChargeCardResponse
    
    private var chargeCardData: ChargeCardData!
    
    init(chargeCardData: ChargeCardData) {
        self.chargeCardData = chargeCardData
    }
    
    var endpoint: String {
        return "payments/charge-card"
    }
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return chargeCardData.params
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
