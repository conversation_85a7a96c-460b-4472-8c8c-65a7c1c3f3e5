//
//  SetCashRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 13/11/2018.
//  Copyright © 2018 GrowFood. All rights reserved.
//

import Foundation

final class SetCashRequest: Requestable {
    
    typealias ResponseType = NewOrderResponse
    
    private var orderIdH: String
    
    init(orderIdH: String) {
        self.orderIdH = orderIdH
    }
    
    var endpoint: String {
        return "payments/set-cash"
    }
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        let params: [String : Any] = ["orderId_H": orderIdH]
        return params
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
