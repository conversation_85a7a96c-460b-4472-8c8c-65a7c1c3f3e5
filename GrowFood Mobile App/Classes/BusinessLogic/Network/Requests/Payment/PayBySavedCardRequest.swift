//
//  PayBySavedCardRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 03.07.2020.
//  Copyright © 2020 GrowFood. All rights reserved.
//

import Foundation

final class PayBySavedCardRequest: Requestable {
    
    typealias ResponseType = ChargeCardResponse
    
    private var paymentData: PaymentData!
    
    init(paymentData: PaymentData) {
        self.paymentData = paymentData
    }
    
    var endpoint: String {
        switch paymentData.type {
        case .order:
            return "payments/charge-token"
        case .tips:
            return "payments/tip/charge-token"
        }
    }
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        let cardId = paymentData.selectedPaymentType?.cardId ?? ""
        switch paymentData.type {
        case .order(_, let orderIdsHU, _, _):
            return ["orderIds_HU": orderIdsHU,
                    "externalClientCardId_H": cardId]
        case .tips(ratingTaskId: let taskId, tipAmountId: let amoundId):
            return ["tipAmountId": amoundId,
                    "ratingTaskId": taskId,
                    "externalClientCardId_H": cardId]
        }
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
