//
//  CustomizationInfoRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 14/03/2019.
//  Copyright © 2019 GrowFood. All rights reserved.
//

import Foundation

final class CustomizationInfoRequest: Requestable {
    
    typealias ResponseType = CustomizationInfoResponse
    
    var pricePlanId: Int
    var menuType: String
    var addressIdH: String? = nil
    var firstDeliveryDate: Date? = nil
    
    init(pricePlanId: Int,
         menuType: String,
         addressIdH: String?,
         firstDeliveryDate: Date?) {
        self.pricePlanId = pricePlanId
        self.menuType = menuType
        self.addressIdH = addressIdH
        self.firstDeliveryDate = firstDeliveryDate
    }
    
    var endpoint: String = "customization-info/possible-dates"
    
    var method: Network.Method = .get
    
    var query: Network.QueryType = .path
    
    var parameters: [String : Any]? {
        var params: [String : Any] = ["cityId": UserService.shared.cityId]
        
        params["pricePlanId"] = pricePlanId
        params["menuType"] = menuType
        
        if let addressIdH = addressIdH {
            params["clientAddressId_H"] = addressIdH
        }
        if let firstDeliveryDate {
            let dateFormatter = DateFormatter.yyyyMMdd
            params["firstDeliveryDate"] = dateFormatter.string(from: firstDeliveryDate)
        }
        return params
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 20
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
