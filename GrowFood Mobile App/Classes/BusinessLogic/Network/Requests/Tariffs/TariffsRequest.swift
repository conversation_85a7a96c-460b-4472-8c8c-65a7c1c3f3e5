//
//  TariffsRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 30/08/2018.
//  Copyright © 2018 GrowFood. All rights reserved.
//

import Foundation


final class GetTariffsRequestt: Requestable {
    
    typealias ResponseType = TariffsResponse
    
    var week: String = ""
    
    init(week: String) {
        self.week = week
    }
    
    var endpoint: String {
        return "menus/mobile_apps_v2/promotion/\(week).json"
    }
    var method: Network.Method = .get
    
    var query: Network.QueryType = .path
    
    var parameters: [String : Any]? {
        return ["1":Date().timeIntervalSince1970]
    }
    
    var headers: [String : String]? {
        return defaultHeaders
    }
    
    var baseUrl: URL {
        return storageBaseUrl
    }
    
    var timeout: TimeInterval {
        return 40
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
