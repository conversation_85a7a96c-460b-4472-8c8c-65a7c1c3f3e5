//
//  GetAlertsRequest.swift
//  GrowFood Mobile App
//
//  Created by Medyann<PERSON> on 27.05.2022.
//  Copyright © 2022 GrowFood. All rights reserved.
//

import Foundation

final class GetAlertsRequest: Requestable {
    
    typealias ResponseType = GetAlertsResponse
    
    var endpoint: String {
        return "alerts"
    }
    
    var method: Network.Method = .get
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return nil
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
