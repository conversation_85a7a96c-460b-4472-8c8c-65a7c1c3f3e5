//
//  GetConfirmOptionsRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 18.11.2020.
//  Copyright © 2020 GrowFood. All rights reserved.
//

import Foundation

final class GetConfirmOptionsRequest: Requestable {
    
    typealias ResponseType = ConfirmOptions
    
    private let orderIdH: String
    
    init(orderIdH: String) {
        self.orderIdH = orderIdH
    }
    
    var endpoint: String {
        return "orders/confirm-task/options"
    }
    
    var method: Network.Method = .get
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return [
            "orderId_H": orderIdH
        ]
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
