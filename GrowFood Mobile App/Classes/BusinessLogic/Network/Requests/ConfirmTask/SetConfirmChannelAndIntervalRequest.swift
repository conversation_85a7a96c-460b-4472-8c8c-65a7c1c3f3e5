//
//  SetConfirmChannelAndIntervalRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 18.11.2020.
//  Copyright © 2020 GrowFood. All rights reserved.
//

import Foundation

final class SetConfirmOptionRequest: Requestable {
    
    typealias ResponseType = SetConfirmChannelAndIntervalResponse
    
    private let orderIdH: String
    private let channelId: String
    private let intervalId: String
    
    init(orderIdH: String, channelId: String, intervalId: String) {
        self.orderIdH = orderIdH
        self.channelId = channelId
        self.intervalId = intervalId
    }
    
    var endpoint: String {
        return "orders/confirm-task"
    }
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return [
            "orderId_H": orderIdH,
            "channelId_H": channelId,
            "intervalId_H": intervalId
        ]
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
    
}
