//
//  GetBannersRequest.swift
//  GrowFood Mobile App
//
//  Created by Medyann<PERSON> on 24.05.2022.
//  Copyright © 2022 GrowFood. All rights reserved.
//

import Foundation

final class GetBannersRequest: Requestable {
    
    typealias ResponseType = GetBannersResponse
    
    
    init() {
    }
    
    var endpoint: String {
        if UserService.shared.isAuthorized {
            return "banners"
        }
        else {
            return "banners-no-auth"
        }
    }
    
    var method: Network.Method = .get
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        //TODO: - type  - можно указать типы баннеров, которые необходимо получить.
        return nil
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
