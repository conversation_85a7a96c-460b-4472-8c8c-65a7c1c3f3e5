//
//  OffersBurnRequest.swift
//  GrowFood Mobile App
//
//  Created by Medyann<PERSON> on 24.05.2022.
//  Copyright © 2022 GrowFood. All rights reserved.
//

import Foundation

final class OffersBurnRequest: Requestable {
    
    typealias ResponseType = OffersBurnResponse
    
    var endpoint: String {
        return "clients/offers/burn"
    }
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return [:]
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
