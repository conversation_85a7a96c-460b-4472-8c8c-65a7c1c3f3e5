//
//  RecallRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 27/06/2019.
//  Copyright © 2019 GrowFood. All rights reserved.
//

import Foundation

final class RecallRequest: Requestable {
    
    typealias ResponseType = SetDeliveryTemplateResponse
    
//    var idH: String
    
    init() {
//        self.idH = idH
    }
    
    var endpoint: String {
        return "callback"
    }
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return ["code": "mobile_profile"]
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
    
}
