//
//  GetTemplatesIntervalsRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 11.04.2022.
//  Copyright © 2022 GrowFood. All rights reserved.
//

import Foundation

final class GetTemplatesIntervalsRequest: Requestable {
    
    typealias ResponseType = GetTemplatesIntervalsResponse
    
    init() { }
    
    var endpoint: String {
        return "clients/default-settings/intervals"
    }
    
    var method: Network.Method = .get
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return nil
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
