//
//  SaveIntervalTemplateRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 17.04.2022.
//  Copyright © 2022 GrowFood. All rights reserved.
//

import Foundation

final class SaveIntervalTemplateRequest: Requestable {
    
    typealias ResponseType = SaveTemplateSettingsResponse
    
    let intervalId: Int
    
    init(intervalId: Int) {
        self.intervalId = intervalId
    }
    
    var endpoint: String {
        return "clients/default-settings/intervals"
    }
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return ["intervalId": intervalId]
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
