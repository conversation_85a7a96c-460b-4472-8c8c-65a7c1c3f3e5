//
//  SaveAddressTemplateRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 17.04.2022.
//  Copyright © 2022 GrowFood. All rights reserved.
//

import Foundation

final class SaveAddressTemplateRequest: Requestable {
    
    typealias ResponseType = SaveTemplateSettingsResponse
    
    let clientAddressIdH: String
    
    init(clientAddressIdH: String) {
        self.clientAddressIdH = clientAddressIdH
    }
    
    var endpoint: String {
        return "clients/default-settings/address"
    }
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return ["clientAddressId_H": clientAddressIdH]
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
