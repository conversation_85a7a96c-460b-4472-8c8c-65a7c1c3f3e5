//
//  UpdateClientDetailsRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 09.02.2021.
//  Copyright © 2021 GrowFood. All rights reserved.
//

import Foundation

final class UpdateClientDetailsRequest: Requestable {
    
    typealias ResponseType = GetProfileResponse
    
    private let imtModel: IMTModel
    
    init(imtModel: IMTModel) {
        self.imtModel = imtModel
    }
    
    var endpoint: String {
        return "client-details/update-details"
    }
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        let dateFormatter = DateFormatter.yyyyMMdd
        var params: [String : Any] = [:]
        if imtModel.menuTypeGroupIdH != nil {
            params = [
                "height": imtModel.height,
                "weight": imtModel.weight,
                "lifestyleId_H": imtModel.activityId,
                "gender": imtModel.sex == .men ? "m" : "f",
                "birthday": dateFormatter.string(from: imtModel.dob)] as [String : Any]
            if imtModel.targetWeight != 0 {
                params["purposeWeight"] = imtModel.targetWeight
            }
            if imtModel.menuTypeGroupIdH != "" {
                if imtModel.menuTypeGroupIdH != nil {
                    params["menuTypeGroupId_H"] = imtModel.menuTypeGroupIdH!
                }
            }
        }
        else {
            params = [
                "height": imtModel.height,
                "weight": imtModel.weight,
                "lifestyleId_H": imtModel.activityId,
                "gender": imtModel.sex == .men ? "m" : "f",
                "birthday": dateFormatter.string(from: imtModel.dob)] as [String : Any]
            params["menuTypeGroupId_H"] = NSNull()
        }
        if imtModel.weight <= Double(imtModel.targetWeight) {
            params["purposeWeight"] = nil
        }
        return params
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
