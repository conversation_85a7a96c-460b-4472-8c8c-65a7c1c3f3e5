//
//  GetLifestylesRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 03.02.2021.
//  Copyright © 2021 GrowFood. All rights reserved.
//

import Foundation

final class GetLifestylesRequest: Requestable {
    
    typealias ResponseType = GetLifestylesResponse
    
    var endpoint: String {
        return "client-details/lifestyles"
    }
    
    var method: Network.Method = .get
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return nil
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 20
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
