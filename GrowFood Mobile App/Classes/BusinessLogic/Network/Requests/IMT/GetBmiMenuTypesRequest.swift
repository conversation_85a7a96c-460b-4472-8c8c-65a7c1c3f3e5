//
//  GetBmiMenuTypesRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 06.03.2023.
//  Copyright © 2023 GrowFood. All rights reserved.
//

import Foundation

final class GetBmiMenuTypesRequest: Requestable {
    
    typealias ResponseType = GetMenuTypesBMIResponse
    
    let cityId: Int
    let isLongation: Bool
    let clientAddressIdH: String?
    
    init(cityId: Int,
         clientAddressIdH: String?,
         isLongation: Bool) {
        self.clientAddressIdH = clientAddressIdH
        self.cityId = cityId
        self.isLongation = isLongation
    }
    
    var endpoint: String {
        return "menu/bmi-menutypes"
    }
    
    var method: Network.Method = .get
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        var params: [String : Any] = ["cityId": cityId,
                                      "isLongation": isLongation ? 1 : 0]
        if let clientAddressIdH = clientAddressIdH {
            params["clientAddressId_H"] = clientAddressIdH
        }
        return params
    }
    
    var headers: [String : String]? {
        return UserService.shared.isAuthorized ? authHeaders : notAuthHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 60
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
