//
//  GetPossiblePreferencesRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 01/04/2019.
//  Copyright © 2019 GrowFood. All rights reserved.
//

import Foundation

final class GetPossiblePreferencesRequest: Requestable {
    
    typealias ResponseType = GetPossiblePreferencesResponse
    
    
    var orderId: String? = nil
    
    init(orderId: String?) {
        self.orderId = orderId
    }
    
    var endpoint: String {
        return "prefs/for-favorites"
    }
    
    var method: Network.Method = .get
    
    var query: Network.QueryType = .path
    
    var parameters: [String : Any]? {
        if orderId != nil {
            return ["orderId_H": orderId!]
        }
        return nil
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 20
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
