//
//  GetPreferencesRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 30/03/2019.
//  Copyright © 2019 GrowFood. All rights reserved.
//

import Foundation

final class GetPreferencesRequest: Requestable {
    
    typealias ResponseType = GetPreferencesResponse
    
    let grouped: Bool
    
    init(grouped: Bool) {
        self.grouped = grouped
    }
    
    var endpoint: String {
        return "prefs"
    }
    
    
    var method: Network.Method = .get
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return ["grouped": grouped ? 1 : 0]
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 60
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
