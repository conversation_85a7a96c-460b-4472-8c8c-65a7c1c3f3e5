//
//  RemovePreferenceRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 01/04/2019.
//  Copyright © 2019 GrowFood. All rights reserved.
//

import Foundation

final class RemovePreferenceRequest: Requestable {
    
    typealias ResponseType = GetClientAddressesResponse
    
    private var packId: Int
    private var isFavorite: Bool
    
    init(packId: Int,
         isFavorite: Bool) {
        self.packId = packId
        self.isFavorite = isFavorite
    }
    
    var endpoint: String {
        return "prefs"
    }
    
    var method: Network.Method = .delete
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        let params: [String : Any] = ["isFavorite": isFavorite,
                                      "packId": packId]
        return params
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 60
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
