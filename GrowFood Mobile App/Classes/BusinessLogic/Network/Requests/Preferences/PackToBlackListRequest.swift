//
//  PackToBlackListRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 29/03/2019.
//  Copyright © 2019 GrowFood. All rights reserved.
//

import Foundation

final class PackToBlackListRequest: Requestable {
    
    typealias ResponseType = GetFeedbackTasksResponse
    
    var packId: Int! = 0
    var isFavorite: Bool = false
    
    init(packId: Int, isFavorite: Bool = false) {
        self.packId = packId
        self.isFavorite = isFavorite   
    }
    
    var endpoint: String {
        return "prefs"
    }
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return ["isFavorite": isFavorite,
                "packId": packId!]
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 60
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
