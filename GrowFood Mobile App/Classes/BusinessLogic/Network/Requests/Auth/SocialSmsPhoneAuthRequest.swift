//
//  SocialSmsPhoneAuthRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 24/08/2018.
//  Copyright © 2018 GrowFood. All rights reserved.
//

import Foundation

final class SocialSmsPhoneAuthRequest: Requestable {
    
    typealias ResponseType = SmsPhoneAuthResponse
    
    private var phone: String
    private var code: String
    private var socialData: SocialData!
    
    init(phone: String, code: String, socialData: SocialData) {
        self.phone = phone
        self.code = code
        self.socialData = socialData
    }
    
    var endpoint: String {
        return "auth/\(socialData.socialType.urlPath)/register"
    }
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        var params: [String : Any] = ["phone": phone,
                                      "password": code,
                                      "socialToken": socialData.token!]
        if let idfa = GFAnalytics.shared.idfa {
            params["idfa"] = idfa
        }
        if let appMetricaId = GFAnalytics.shared.appMetricaId {
            params["appMetricaId"] = appMetricaId
        }
        if let afParams = GFUserDefaults.shared.afFull {
            params["af_full"] = afParams
        }
        return params
    }
    
    var headers: [String : String]? {
        return notAuthHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 20
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
