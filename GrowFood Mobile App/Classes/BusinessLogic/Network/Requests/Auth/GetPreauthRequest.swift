//
//  GetPreauthRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 08.08.2020.
//  Copyright © 2020 GrowFood. All rights reserved.
//

import Foundation

final class GetPreauthRequest: Requestable {
    
    typealias ResponseType = GetPreauthResponse
    
    
    var endpoint: String = "auth/preauth"
    
    var method: Network.Method = .get
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        nil
    }
    
    var headers: [String : String]? {
        return notAuthHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 5
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
