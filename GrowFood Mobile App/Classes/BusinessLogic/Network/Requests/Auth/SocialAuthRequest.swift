//
//  SocialAuthRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 23/08/2018.
//  Copyright © 2018 GrowFood. All rights reserved.
//

import Foundation

enum SocialAuthType {
    case vk, fb, apple, tinkoff
    
    var urlPath: String {
        switch self {
        case .fb:
            return "facebook"
        case .vk:
            return "vkid"
        case .apple:
            return "apple"
        case .tinkoff:
            return "tinkoff"
        }
    }
}

struct SocialData {
    let token: String!
    let refreshToken: String?
    let socialType: SocialAuthType!
    let isTinkoffOfferFlow: Bool?
    
    init(socialType: SocialAuthType, token: String, refreshToken: String? = nil, isTinkoffOfferFlow: Bool? = nil) {
        self.token = token
        self.socialType = socialType
        self.refreshToken = refreshToken
        self.isTinkoffOfferFlow = isTinkoffOfferFlow
    }
}

final class SocialAuthRequest: Requestable {
    
    typealias ResponseType = SocialAuthResponse
    
    private var socialData: SocialData
    
    init(socialData: SocialData) {
        self.socialData = socialData
    }
    
    var endpoint: String {
        return "auth/\(socialData.socialType.urlPath)"
    }
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        var params: [String : Any] = ["socialToken": socialData.token ?? ""]
        
        if let refreshToken = socialData.refreshToken {
            params["refreshToken"] = refreshToken
        }
        if let isTinkoffOfferFlow = socialData.isTinkoffOfferFlow {
            params["isTinkoffOfferFlow"] = isTinkoffOfferFlow
        }

        if let idfa = GFAnalytics.shared.idfa {
            params["idfa"] = idfa
        }
        if let appMetricaId = GFAnalytics.shared.appMetricaId {
            params["appMetricaId"] = appMetricaId
        }
        if let afParams = GFUserDefaults.shared.afFull {
            params["af_full"] = afParams
        }
        
        return params
    }
    
    var headers: [String : String]? {
        return notAuthHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 20
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
