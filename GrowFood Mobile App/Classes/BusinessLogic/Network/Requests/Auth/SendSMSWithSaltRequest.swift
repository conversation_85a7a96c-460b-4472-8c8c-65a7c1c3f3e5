//
//  SendSMSWithSaltRequest.swift
//  GrowFood Mobile App
//
//  Created by d.medyannik on 12.12.2024.
//  Copyright © 2024 GrowFood. All rights reserved.
//

import Foundation

final class SendSMSWithSaltRequest: Requestable {
    
    typealias ResponseType = SendSMSResponse
    
    private var phone: String
    
    init(phone: String) {
        self.phone = phone
    }
    
    var endpoint: String = "auth/send-sms"
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        let clientDict = ["phone": phone] as [String : Any]
        return ["client": clientDict]
    }
    
    var headers: [String : String]? {
        return headersWithSoult
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 20
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
