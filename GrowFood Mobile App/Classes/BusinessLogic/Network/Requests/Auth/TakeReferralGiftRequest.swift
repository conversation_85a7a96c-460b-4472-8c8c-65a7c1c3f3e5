//
//  TakeReferralGiftRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 24.07.2020.
//  Copyright © 2020 GrowFood. All rights reserved.
//

final class TakeReferralGiftRequest: Requestable {
    
    typealias ResponseType = TakeReferralGiftResponse
    
    private let promocode: String
    
    init(_ promocode: String) {
        self.promocode = promocode
    }
    
    var endpoint: String = "referrals/charge-bonus"
    
    var method: Network.Method = .get
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return [
            "name_slug": promocode
        ]
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 20
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
