//
//  SmsPhoneAuthRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 22/08/2018.
//  Copyright © 2018 GrowFood. All rights reserved.
//

import Foundation

final class SmsPhoneAuthRequest: Requestable {
    
    typealias ResponseType = SmsPhoneAuthResponse
    
    private var phone: String
    private var code: String
    
    init(phone: String, code: String) {
        self.phone = phone
        self.code = code
    }
    
    var endpoint: String = "auth/login"
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        var params: [String : Any] = ["phone": phone,
                                      "password": code]
        if let idfa = GFAnalytics.shared.idfa {
            params["idfa"] = idfa
        }
        if let appMetricaId = GFAnalytics.shared.appMetricaId {
            params["appMetricaId"] = appMetricaId
        }
        if let afParams = GFUserDefaults.shared.afFull {
            params["af_full"] = afParams
        }
        
        return params
    }
    
    var headers: [String : String]? {
        return notAuthHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 20
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
