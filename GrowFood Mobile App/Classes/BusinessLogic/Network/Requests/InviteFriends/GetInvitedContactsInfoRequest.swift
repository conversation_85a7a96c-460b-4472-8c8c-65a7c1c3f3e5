//
//  GetInvitedContactsInfoRequest.swift
//  GrowFood Mobile App
//
//  Created by Павел Аристов on 09.06.2021.
//  Copyright © 2021 GrowFood. All rights reserved.
//

final class GetInvitedContactsInfoRequest: Requestable {
    
    typealias ResponseType = GetInvitedContactsInfoResponse
    
    private let phones: [String]
    
    init(phones: [String]) {
        self.phones = phones
    }
    
    var endpoint: String {
        return "referrals/contacts"
    }
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return [
            "phones": phones
        ]
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
