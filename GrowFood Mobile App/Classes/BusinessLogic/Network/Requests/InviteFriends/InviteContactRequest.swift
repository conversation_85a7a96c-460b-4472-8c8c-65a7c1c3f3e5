//
//  InviteContactRequest.swift
//  GrowFood Mobile App
//
//  Created by Павел Аристов on 10.06.2021.
//  Copyright © 2021 GrowFood. All rights reserved.
//

import UIKit

final class InviteContactRequest: Requestable {
    
    typealias ResponseType = BaseSuccessResponse
    
    private let phone: String
    
    init(phone: String) {
        self.phone = phone
    }
    
    var endpoint: String {
        return "referrals/invite"
    }
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return [
            "channel": "sms",
            "contact": phone,
            "brandId": 1
        ]
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
