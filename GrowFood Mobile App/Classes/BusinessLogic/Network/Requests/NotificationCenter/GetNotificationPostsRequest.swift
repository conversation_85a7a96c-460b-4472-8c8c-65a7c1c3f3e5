//
//  GetNotificationPostsRequest.swift
//  GrowFood Mobile App
//
//  Created by Павел Аристов on 08.10.2021.
//  Copyright © 2021 GrowFood. All rights reserved.
//

final class GetNotificationPostsRequest: Requestable {
    
    typealias ResponseType = GetNotificationPostsResponse
    
    private let categoryId: String?
    
    init(categoryId: String?) {
        self.categoryId = categoryId
    }
    
    var endpoint: String {
        return "posts"
    }
    
    var method: Network.Method = .get
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        guard let categoryId = categoryId else { return nil }
        return [
            "typeId_H": categoryId
        ]
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
