//
//  SetNotificationPostsRatingStateRequest.swift
//  GrowFood Mobile App
//
//  Created by Павел Аристов on 08.10.2021.
//  Copyright © 2021 GrowFood. All rights reserved.
//

final class SetNotificationPostRatingStateRequest: Requestable {
    
    typealias ResponseType = SetNotificationPostRatingStateResponse
    
    private let postId: String
    private let ratingState: NotificationPost.RatingState
    
    init(postId: String, state: NotificationPost.RatingState) {
        self.postId = postId
        ratingState = state
    }
    
    var endpoint: String {
        let routeSuffix = ratingState == .dislike ? "dislike" : "like"
        return "posts/\(postId)/\(routeSuffix)"
    }
    
    var method: Network.Method {
        return ratingState == .notDetermined ? .delete : .post
    }
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return nil
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
