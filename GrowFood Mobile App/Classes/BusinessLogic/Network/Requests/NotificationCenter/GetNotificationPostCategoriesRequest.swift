//
//  GetNotificationPostCategoriesRequest.swift
//  GrowFood Mobile App
//
//  Created by Паве<PERSON> Аристов on 08.10.2021.
//  Copyright © 2021 GrowFood. All rights reserved.
//

final class GetNotificationPostCategoriesRequest: Requestable {
    
    typealias ResponseType = GetNotificationPostCategoriesResponse
    
    var endpoint: String {
        return "posts/types"
    }
    
    var method: Network.Method = .get
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return nil
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
