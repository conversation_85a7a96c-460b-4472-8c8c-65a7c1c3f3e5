//
//  SetNotificationPostMarkActionRequest.swift
//  GrowFood Mobile App
//
//  Created by Павел Аристов on 08.10.2021.
//  Copyright © 2021 GrowFood. All rights reserved.
//

final class SetNotificationPostMarkActionRequest: Requestable {
    
    typealias ResponseType = BaseSuccessResponse
    
    private let postId: String
    private let markAction: NotificationPost.MarkAction
    
    init(postId: String, markAction: NotificationPost.MarkAction) {
        self.postId = postId
        self.markAction = markAction
    }
    
    var endpoint: String {
        return "posts/\(postId)/action"
    }
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return [
            "actionName": markAction.rawValue
        ]
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
