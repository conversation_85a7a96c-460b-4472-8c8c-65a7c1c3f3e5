//
//  OnlineGiftReadRequest.swift
//  GrowFood Mobile App
//
//  Created by Medyann<PERSON> on 13.10.2022.
//  Copyright © 2022 GrowFood. All rights reserved.
//

final class OnlineGiftReadRequest: Requestable {
    
    typealias ResponseType = BaseSuccessResponse
    
    private let idGift: String
    
    init(idGift: Int) {
        self.idGift = "\(idGift)"
    }
    
    var endpoint: String {
        return "clients/loyalty/online-gift-read"
    }
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return ["id": idGift]
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}

