//
//  LongateLoyaltyStatusRequest.swift
//  GrowFood Mobile App
//
//  Created by Павел Аристов on 01.07.2021.
//  Copyright © 2021 GrowFood. All rights reserved.
//

import Foundation

final class LongateLoyaltyStatusRequest: Requestable {
    
    typealias ResponseType = LongateWalletBonusResponse
    
    var endpoint: String {
        return "clients/loyalty/statuses/longate"
    }
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return nil
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
