//
//  GetLoyaltyAvailableRewards.swift
//  GrowFood Mobile App
//
//  Created by Павел Аристов on 17.08.2021.
//  Copyright © 2021 GrowFood. All rights reserved.
//

final class GetLoyaltyActiveRewardsRequest: Requestable {
    
    typealias ResponseType = GetLoyaltyActiveRewardsResponse
    
    var endpoint: String {
        return "clients/loyalty/available-rewards"
    }
    
    var method: Network.Method = .get
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return nil
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
