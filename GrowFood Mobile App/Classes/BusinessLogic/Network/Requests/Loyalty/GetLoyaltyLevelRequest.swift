//
//  GetLoyaltyLevelRequest.swift
//  GrowFood Mobile App
//
//  Created by Павел Аристов on 28.04.2021.
//  Copyright © 2021 GrowFood. All rights reserved.
//

final class GetLoyaltyLevelsRequest: Requestable {
    
    typealias ResponseType = GetLoyaltyLevelsResponse
    
    var endpoint: String {
        return "clients/loyalty/statuses"
    }
    
    var method: Network.Method = .get
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return nil
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
