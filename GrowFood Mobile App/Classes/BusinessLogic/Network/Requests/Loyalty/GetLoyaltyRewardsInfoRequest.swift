//
//  GetLoyaltyRewardsInfoRequest.swift
//  GrowFood Mobile App
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 13.10.2022.
//  Copyright © 2022 GrowFood. All rights reserved.
//

final class GetLoyaltyRewardsInfoRequest: Requestable {
    
    typealias ResponseType = GetLoyaltyRewardsInfoResponse
    
    var endpoint: String {
        return "clients/loyalty/rewards-info"
    }
    
    var method: Network.Method = .get
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return nil
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}

