//
//  SendPartnerGiftRequest.swift
//  GrowFood Mobile App
//
//  Created by Павел Аристов on 10.11.2021.
//  Copyright © 2021 GrowFood. All rights reserved.
//

final class SendPartnerGiftRequest: Requestable {
    
    typealias ResponseType = BaseSuccessResponse
    
    private let email: String
    private let gift: PartnerGift
    private let saveEmailAsDefault: Bool
    
    init(gift: PartnerGift, email: String, saveEmailAsDefault: Bool) {
        self.gift = gift
        self.email = email
        self.saveEmailAsDefault = saveEmailAsDefault
    }
    
    var endpoint: String {
        return "clients/loyalty/online-gift-send"
    }
    
    var method: Network.Method = .post
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return [
            "id": gift.id,
            "email": email,
            "saveEmailAsDefault": saveEmailAsDefault
        ]
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
