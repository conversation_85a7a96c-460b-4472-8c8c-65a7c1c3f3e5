//
//  GetPartnerGiftsRequest.swift
//  GrowFood Mobile App
//
//  Created by Павел Аристов on 09.11.2021.
//  Copyright © 2021 GrowFood. All rights reserved.
//

final class GetPartnerGiftsRequest: Requestable {
    
    typealias ResponseType = GetPartnerGiftsResponse
    
    var endpoint: String {
        return "clients/loyalty/online-gifts"
    }
    
    var method: Network.Method = .get
    
    var query: Network.QueryType = .json
    
    var parameters: [String : Any]? {
        return nil
    }
    
    var headers: [String : String]? {
        return authHeaders
    }
    
    var baseUrl: URL {
        return defaultBaseUrl
    }
    
    var timeout: TimeInterval {
        return 50
    }
    
    var cachePolicy: NSURLRequest.CachePolicy {
        return .reloadIgnoringLocalAndRemoteCacheData
    }
}
