//
//  MenuTypeDescriptionCollectionViewCell.swift
//  GrowFood Mobile App
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 28.09.2023.
//  Copyright © 2023 GrowFood. All rights reserved.
//

import UIKit

final class MenuTypeDescriptionCollectionViewCell: BaseCardCollectionViewCell {

    @IBOutlet weak var priceLabel: GFLabelB3Regular!
    @IBOutlet weak var mealsLabel: GFLabelB3Regular!
    @IBOutlet weak var kcalLabel: GFLabelB3Regular!
    @IBOutlet weak var titleLabel: GFLabelH4!
    @IBOutlet weak var descrLabel: GFLabelB2Regular!
    override func awakeFromNib() {
        super.awakeFromNib()
        // Initialization code
    }
    
    func setViewModel(_ vm: MenuTypeCardViewModel, forceGrayColor: Bool) {
        descrLabel.text = vm.shortDescription
        titleLabel.text = vm.title
        priceLabel.text = vm.priceTitle
        kcalLabel.text = vm.kcalTitle
        mealsLabel.text = vm.numberOfMealsString
        if !forceGrayColor {
            self.backgroundColor = UIColor(hex: vm.backgroundColor ?? "#ffffff")
        }
        else {
            self.backgroundColor = ColorStyle.Black.grey4.value
        }
    }

}
