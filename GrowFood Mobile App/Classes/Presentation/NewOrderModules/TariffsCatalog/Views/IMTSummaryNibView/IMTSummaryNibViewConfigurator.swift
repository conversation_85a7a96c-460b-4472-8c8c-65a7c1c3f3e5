//
//  IMTSummaryNibViewConfigurator.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 23/05/2025.
//  Copyright © 2024 GrowFood. All rights reserved.
//

import UIKit

enum IMTSummaryNibViewConfigurator {
    
    // MARK: - Internal methods
    
    static func configure(menuTypeGroupIdH: String,
                          menuTypeCode: String,
                          isFirstTime: Bool,
                          firstDeliveryDate: Date? = nil,
                          firstDeliverySession: String? = nil,
                          sectionId: String? = nil,
                          delegate: IMTSummaryNibViewDelegate? = nil) -> IMTSummaryNibView {
        
        let nibView = IMTSummaryNibView()
        nibView.delegate = delegate
        
        nibView.configure(menuTypeGroupIdH: menuTypeGroupIdH,
                         menuTypeCode: menuTypeCode,
                         isFirstTime: isFirstTime,
                         firstDeliveryDate: firstDeliveryDate,
                         firstDeliverySession: firstDeliverySession,
                         sectionId: sectionId)
        
        return nibView
    }
}
