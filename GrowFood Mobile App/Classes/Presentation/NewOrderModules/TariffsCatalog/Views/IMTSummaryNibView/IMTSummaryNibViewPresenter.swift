//
//  IMTSummaryNibViewPresenter.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 23/05/2025.
//  Copyright © 2024 GrowFood. All rights reserved.
//

import Foundation

protocol IMTSummaryNibViewPresenterDelegate: AnyObject {
    func openMenuType(menuType: MenuTypeBmiViewModel,
                      firstMealDate: Date?,
                      firstMealSession: String?,
                      sectionId: String)
    func openInfoBmi(viewModel: MenuTypeBmiViewModel)
    func openEditWeight()
    func openFaq()
    func openNormCalories()
    func openEditBMI()
    func openAllFilters(delegate: MenuFilterSettingsModuleDelegate)
}

final class IMTSummaryNibViewPresenter {
    
    // MARK: - Properties
    weak var view: IMTSummaryNibViewInput?
    var interactor: IMTSummaryNibViewInteractor?
    weak var delegate: IMTSummaryNibViewPresenterDelegate?
    
    var sectionId: String = ""
    private var menuType: MenuTypeBmiViewModel?
    
    // MARK: - Public Properties
    var groups: [IMTSummaryGroupViewModel] = []
    
    // MARK: - Public Methods
    func viewWillAppear() {
        interactor?.loadData()
    }
    
    func setGroups(_ groups: [IMTSummaryGroupViewModel]) {
        self.groups = groups
        view?.reloadData()
    }
    
    func openGoalAt(indexPath: IndexPath) {
        let group = self.groups[indexPath.section-1]
        if let menuType = group.menuTypeBmiModels[safe: indexPath.item - 1] {
            /// Если необходима миграция на новые фильтры
            if UserService.shared.isProposeFiltersChange {
                self.menuType = menuType
                delegate?.openAllFilters(delegate: self)
            }
            else {
                delegate?.openMenuType(menuType: menuType,
                                     firstMealDate: interactor?.firstDeliveryDate,
                                     firstMealSession: interactor?.firstDeliverySession,
                                     sectionId: sectionId)
            }
        }
    }
    
    func openInfoAt(indexPath: IndexPath) {
        let group = self.groups[indexPath.section-1]
        if let menuType = group.menuTypeBmiModels[safe: indexPath.item - 1] {
            delegate?.openInfoBmi(viewModel: menuType)
        }
    }
    
    func openEditWeight() {
        delegate?.openEditWeight()
    }
    
    func openFaq() {
        delegate?.openFaq()
    }
    
    func openNormCalories() {
        delegate?.openNormCalories()
    }
    
    func openEditBMI() {
        delegate?.openEditBMI()
    }
}

// MARK: - IMTSummaryNibViewInteractorOutput
extension IMTSummaryNibViewPresenter: IMTSummaryNibViewInteractorOutput {
    func didLoadGroups(_ groups: [IMTSummaryGroupViewModel]) {
        setGroups(groups)
    }
    
    func didFailWithError(_ error: GFError) {
        view?.showError(error)
    }
}

// MARK: - MenuFilterSettingsModuleDelegate
extension IMTSummaryNibViewPresenter: MenuFilterSettingsModuleDelegate {
//    func didSelectFilters(_ filters: [MenuFilterViewModel]) {
//        guard let menuType = self.menuType else { return }
//        delegate?.openMenuType(menuType: menuType,
//                             firstMealDate: interactor?.firstDeliveryDate,
//                             firstMealSession: interactor?.firstDeliverySession,
//                             sectionId: sectionId)
//    }
}
