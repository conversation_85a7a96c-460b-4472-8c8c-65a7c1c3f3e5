<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="21701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21678"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="IMTSummaryNibView" customModule="GrowFood_Mobile_App" customModuleProvider="target">
            <connections>
                <outlet property="collectionView" destination="8bC-Xf-vdC" id="gvf-OV-Yhg"/>
                <outlet property="loadingView" destination="N2e-h3-5Hd" id="Qnd-gd-4Hd"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="iN0-l3-epB">
            <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <collectionView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" dataMode="none" translatesAutoresizingMaskIntoConstraints="NO" id="8bC-Xf-vdC">
                    <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                    <collectionViewFlowLayout key="collectionViewLayout" automaticEstimatedItemSize="YES" minimumLineSpacing="10" minimumInteritemSpacing="10" id="cBb-1e-fHI">
                        <size key="itemSize" width="128" height="128"/>
                        <size key="headerReferenceSize" width="0.0" height="0.0"/>
                        <size key="footerReferenceSize" width="0.0" height="0.0"/>
                        <inset key="sectionInset" minX="0.0" minY="0.0" maxX="0.0" maxY="0.0"/>
                    </collectionViewFlowLayout>
                </collectionView>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="N2e-h3-5Hd" customClass="LoadingView" customModule="GrowFood_Mobile_App" customModuleProvider="target">
                    <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                </view>
            </subviews>
            <viewLayoutGuide key="safeArea" id="vUN-kp-3ea"/>
            <color key="backgroundColor" systemColor="systemBackgroundColor"/>
            <constraints>
                <constraint firstItem="8bC-Xf-vdC" firstAttribute="leading" secondItem="vUN-kp-3ea" secondAttribute="leading" id="2jI-Xh-g0e"/>
                <constraint firstItem="N2e-h3-5Hd" firstAttribute="leading" secondItem="vUN-kp-3ea" secondAttribute="leading" id="6Yz-Nh-fhd"/>
                <constraint firstItem="vUN-kp-3ea" firstAttribute="trailing" secondItem="8bC-Xf-vdC" secondAttribute="trailing" id="7Ub-Xh-g0e"/>
                <constraint firstItem="vUN-kp-3ea" firstAttribute="bottom" secondItem="8bC-Xf-vdC" secondAttribute="bottom" id="8Ub-Xh-g0e"/>
                <constraint firstItem="vUN-kp-3ea" firstAttribute="trailing" secondItem="N2e-h3-5Hd" secondAttribute="trailing" id="N6z-Nh-fhd"/>
                <constraint firstItem="vUN-kp-3ea" firstAttribute="bottom" secondItem="N2e-h3-5Hd" secondAttribute="bottom" id="P8z-Nh-fhd"/>
                <constraint firstItem="8bC-Xf-vdC" firstAttribute="top" secondItem="vUN-kp-3ea" secondAttribute="top" id="Q2I-Xh-g0e"/>
                <constraint firstItem="N2e-h3-5Hd" firstAttribute="top" secondItem="vUN-kp-3ea" secondAttribute="top" id="R6z-Nh-fhd"/>
            </constraints>
            <point key="canvasLocation" x="132" y="154"/>
        </view>
    </objects>
    <resources>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
