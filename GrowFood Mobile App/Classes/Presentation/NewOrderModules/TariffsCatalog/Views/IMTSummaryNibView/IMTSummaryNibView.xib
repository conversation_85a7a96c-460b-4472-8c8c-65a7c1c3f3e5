<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="23504" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="23506"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="IMTSummaryNibView" customModule="GrowFood_Mobile_App" customModuleProvider="target">
            <connections>
                <outlet property="loadingView" destination="N2e-h3-5Hd" id="Qnd-gd-4Hd"/>
                <outlet property="stackView" destination="sTk-Vw-123" id="sTk-Vw-456"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="iN0-l3-epB">
            <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="sTk-Vw-123">
                    <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                </stackView>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="N2e-h3-5Hd" customClass="LoadingView" customModule="GrowFood_Mobile_App" customModuleProvider="target">
                    <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                </view>
            </subviews>
            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstItem="sTk-Vw-123" firstAttribute="leading" secondItem="iN0-l3-epB" secondAttribute="leading" id="2jI-Xh-g0e"/>
                <constraint firstItem="N2e-h3-5Hd" firstAttribute="leading" secondItem="iN0-l3-epB" secondAttribute="leading" id="6Yz-Nh-fhd"/>
                <constraint firstAttribute="trailing" secondItem="sTk-Vw-123" secondAttribute="trailing" id="7Ub-Xh-g0e"/>
                <constraint firstAttribute="bottom" secondItem="sTk-Vw-123" secondAttribute="bottom" id="8Ub-Xh-g0e"/>
                <constraint firstAttribute="trailing" secondItem="N2e-h3-5Hd" secondAttribute="trailing" id="N6z-Nh-fhd"/>
                <constraint firstAttribute="bottom" secondItem="N2e-h3-5Hd" secondAttribute="bottom" id="P8z-Nh-fhd"/>
                <constraint firstItem="sTk-Vw-123" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" id="Q2I-Xh-g0e"/>
                <constraint firstItem="N2e-h3-5Hd" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" id="R6z-Nh-fhd"/>
            </constraints>
            <point key="canvasLocation" x="130.53435114503816" y="153.52112676056339"/>
        </view>
    </objects>
</document>
