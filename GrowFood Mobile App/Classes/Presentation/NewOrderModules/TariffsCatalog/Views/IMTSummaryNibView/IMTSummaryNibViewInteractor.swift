//
//  IMTSummaryNibViewInteractor.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 23/05/2025.
//  Copyright © 2024 GrowFood. All rights reserved.
//

import Foundation

protocol IMTSummaryNibViewInteractorOutput: AnyObject {
    var groups: [IMTSummaryGroupViewModel] { get }
    
    func didLoadGroups(_ groups: [IMTSummaryGroupViewModel])
    func didFailWithError(_ error: GFError)
}

final class IMTSummaryNibViewInteractor {
    
    // MARK: - Properties
    weak var presenter: IMTSummaryNibViewInteractorOutput?
    
    var menuTypeGroupId: String = ""
    var menuTypeCode: String = ""
    var firstDeliveryDate: Date?
    var firstDeliverySession: String?
    
    private let catalogBMIService = MenuCatalogBMIService()
    
    // MARK: - Public Methods
    func configure(menuTypeGroupIdH: String,
                   menuTypeCode: String,
                   firstDeliveryDate: Date? = nil,
                   firstDeliverySession: String? = nil) {
        self.menuTypeGroupId = menuTypeGroupIdH
        self.menuTypeCode = menuTypeCode
        self.firstDeliveryDate = firstDeliveryDate
        self.firstDeliverySession = firstDeliverySession
    }
    
    func loadData() {
        loadCatalogData()
    }
    
    // MARK: - Private Methods
    private func loadCatalogData() {
        let clientAddressIdH = UserService.shared.curUserSettings?.userConfig?.defaultSettings?.address?.idH
        let forceReload = presenter?.groups.count ?? 0 > 0
        
        Task {
            let groupsRequest = await catalogBMIService.getImtSummaryFor(
                clientAddressIdH: clientAddressIdH,
                forceReload: forceReload,
                firstDeliveryDate: firstDeliveryDate,
                session: firstDeliverySession)
            
            switch groupsRequest {
            case .success(let groups):
                DispatchQueue.main.async {
                    self.presenter?.didLoadGroups(groups)
                }
            case .failure(let error):
                DispatchQueue.main.async {
                    self.presenter?.didFailWithError(error)
                }
            }
        }
    }
}
