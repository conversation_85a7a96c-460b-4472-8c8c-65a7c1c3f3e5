//
//  IMTSummaryNibView.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 23/05/2025.
//  Copyright © 2024 GrowFood. All rights reserved.
//

import UIKit

protocol IMTSummaryNibViewDelegate: AnyObject {
    func openMenuType(menuType: MenuTypeBmiViewModel,
                      firstMealDate: Date?,
                      firstMealSession: String?,
                      sectionId: String)
    func openInfoBmi(viewModel: MenuTypeBmiViewModel)
    func openEditWeight()
    func openFaq()
    func openNormCalories()
    func openEditBMI()
    func openAllFilters(delegate: MenuFilterSettingsModuleDelegate)
}

final class IMTSummaryNibView: NibView {

    // MARK: - Outlets
    @IBOutlet weak var collectionView: UICollectionView!
    @IBOutlet weak var loadingView: LoadingView!

    // MARK: - Properties
    weak var delegate: IMTSummaryNibViewDelegate?

    private var presenter: IMTSummaryNibViewPresenter?
    private var interactor: IMTSummaryNibViewInteractor?

    var isFirstTime: Bool = false
    var sectionId: String = ""
    var userHasMenuTypeGroup = false

    // MARK: - Lifecycle
    override func setupView() {
        super.setupView()
        setupVIPER()
        setupCollectionView()
        setupLoadingView()
        if let curUser = UserService.shared.curUser {
            userHasMenuTypeGroup = curUser.bmiFilled && !isFirstTime
        }
    }

    // MARK: - Public Methods
    func configure(menuTypeGroupIdH: String,
                   menuTypeCode: String,
                   isFirstTime: Bool,
                   firstDeliveryDate: Date? = nil,
                   firstDeliverySession: String? = nil,
                   sectionId: String? = nil) {
        self.isFirstTime = isFirstTime
        self.sectionId = sectionId ?? ""

        interactor?.configure(menuTypeGroupIdH: menuTypeGroupIdH,
                             menuTypeCode: menuTypeCode,
                             firstDeliveryDate: firstDeliveryDate,
                             firstDeliverySession: firstDeliverySession)

        loadData()
    }

    func loadData() {
        showLoading()
        presenter?.viewWillAppear()
    }

    // MARK: - Private Methods
    private func setupVIPER() {
        let presenter = IMTSummaryNibViewPresenter()
        let interactor = IMTSummaryNibViewInteractor()

        self.presenter = presenter
        self.interactor = interactor

        presenter.view = self
        presenter.interactor = interactor
        presenter.delegate = self

        interactor.presenter = presenter
    }

    private func setupCollectionView() {
        collectionView.delegate = self
        collectionView.dataSource = self
        collectionView.backgroundColor = .clear

        // Регистрируем ячейки
        collectionView.register(UINib(nibName: "IMTSummaryGoalHeaderCollectionViewCell", bundle: nil),
                               forCellWithReuseIdentifier: "IMTSummaryGoalHeaderCollectionViewCell")
        collectionView.register(UINib(nibName: "IMTSummaryGoalItemCollectionViewCell", bundle: nil),
                               forCellWithReuseIdentifier: "IMTSummaryGoalItemCollectionViewCell")
        collectionView.register(UINib(nibName: "IMTSummaryDecsriptionCollectionViewCell", bundle: nil),
                               forCellWithReuseIdentifier: "IMTSummaryDecsriptionCollectionViewCell")

        // Настройка layout
        if let layout = collectionView.collectionViewLayout as? UICollectionViewFlowLayout {
            layout.minimumLineSpacing = 0
            layout.minimumInteritemSpacing = 0
        }
    }

    private func setupLoadingView() {
        loadingView.delegate = self
        loadingView.isHidden = true
    }

    private func showLoading() {
        loadingView.isHidden = false
        collectionView.isHidden = true
    }

    private func hideLoading() {
        loadingView.isHidden = true
        collectionView.isHidden = false
    }
}

// MARK: - IMTSummaryNibViewInput
extension IMTSummaryNibView: IMTSummaryNibViewInput {
    func reloadData() {
        DispatchQueue.main.async {
            self.hideLoading()
            self.collectionView.reloadData()
        }
    }

    func showError(_ error: GFError) {
        DispatchQueue.main.async {
            self.loadingView.showError(error)
        }
    }
}

// MARK: - LoadingViewDelegate
extension IMTSummaryNibView: LoadingViewDelegate {
    func tappedReload(fromMenu: Bool) {
        loadData()
    }
}

// MARK: - IMTSummaryNibViewPresenterDelegate
extension IMTSummaryNibView: IMTSummaryNibViewPresenterDelegate {
    func openMenuType(menuType: MenuTypeBmiViewModel,
                      firstMealDate: Date?,
                      firstMealSession: String?,
                      sectionId: String) {
        delegate?.openMenuType(menuType: menuType,
                              firstMealDate: firstMealDate,
                              firstMealSession: firstMealSession,
                              sectionId: sectionId)
    }

    func openInfoBmi(viewModel: MenuTypeBmiViewModel) {
        delegate?.openInfoBmi(viewModel: viewModel)
    }

    func openEditWeight() {
        delegate?.openEditWeight()
    }

    func openFaq() {
        delegate?.openFaq()
    }

    func openNormCalories() {
        delegate?.openNormCalories()
    }

    func openEditBMI() {
        delegate?.openEditBMI()
    }

    func openAllFilters(delegate: MenuFilterSettingsModuleDelegate) {
        self.delegate?.openAllFilters(delegate: delegate)
    }
}

// MARK: - UICollectionViewDataSource
extension IMTSummaryNibView: UICollectionViewDataSource {
    func numberOfSections(in collectionView: UICollectionView) -> Int {
        return (presenter?.groups.count ?? 0) == 0 ? 0 : presenter!.groups.count + 1
    }

    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        if section == 0 {
            return 1
        } else {
            let group = presenter!.groups[section - 1]
            return group.menuTypeBmiModels.count + 1
        }
    }

    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        if indexPath.section == 0 {
            let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "IMTSummaryDecsriptionCollectionViewCell", for: indexPath) as! IMTSummaryDecsriptionCollectionViewCell
            cell.delegate = self
            if isFirstTime {
                cell.changeWeightView.isHidden = true
            }
            return cell
        } else {
            guard let group = presenter?.groups[indexPath.section-1] else { return UICollectionViewCell() }
            if indexPath.item == 0 {
                let cell = collectionView.dequeueReusableCell(forIndexPath: indexPath) as IMTSummaryGoalHeaderCollectionViewCell
                cell.setGroupViewModel(group)
                return cell
            }
            else {
                let cell = collectionView.dequeueReusableCell(forIndexPath: indexPath) as IMTSummaryGoalItemCollectionViewCell
                cell.setGroup(group,
                              at: indexPath)
                cell.delegate = self
                return cell
            }
        }
    }
    
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        let screenWidth = UIScreen.main.bounds.width
        if indexPath.section == 0 {
            return CGSize(width: screenWidth,
                          height: userHasMenuTypeGroup ? 140 : 64)
        }
        else {
            
            let group = presenter!.groups[indexPath.section-1]
            if indexPath.item == 0 {
                return CGSize(width: screenWidth,
                              height: group.heightHeader)
            }
            else {
                let group = presenter!.groups[indexPath.section-1]
                return CGSize(width: screenWidth,
                              height: group.heightAt(indexPath: indexPath))
            }
        }
    }
    
//    func collectionView(_ collectionView: UICollectionView, viewForSupplementaryElementOfKind kind: String, at indexPath: IndexPath) -> UICollectionReusableView {
//        let footerView = collectionView.dequeueReusableSupplementaryView(ofKind: kind, withReuseIdentifier: "footer", for: indexPath as IndexPath)
//        
//        return footerView
//    }
}

// MARK: - UICollectionViewDelegate
extension IMTSummaryNibView: UICollectionViewDelegate {
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        if indexPath.item > 0 {
            presenter?.openGoalAt(indexPath: indexPath)
        }
        if indexPath == IndexPath(item: 0, section: 0) {
            if let curUser = UserService.shared.curUser {
                if curUser.menuTypeGroupIdH != "" {
                    presenter?.openEditWeight()
                }
            }
        }
    }
}

// MARK: - UICollectionViewDelegateFlowLayout
extension IMTSummaryNibView: UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, referenceSizeForFooterInSection section: Int) -> CGSize {
        if section == (presenter?.groups.count ?? 0) {
            return CGSize(width: 0, height: 0)
        }
        else {
            return CGSize(width: UIScreen.main.bounds.width,
                            height: 8)
        }
    }
}

// MARK: - IMTSummaryGoalItemCollectionViewCellDelegate
extension IMTSummaryNibView: IMTSummaryGoalItemCollectionViewCellDelegate {
    func selectedAt(indexPath: IndexPath) {
        presenter?.openGoalAt(indexPath: indexPath)
    }

    func tappedInfoAt(indexPath: IndexPath) {
        presenter?.openInfoAt(indexPath: indexPath)
    }
}

// MARK: - IMTSummaryDecsriptionCollectionViewCellDelegate
extension IMTSummaryNibView: IMTSummaryDecsriptionCollectionViewCellDelegate {
    func openNormalCalories() {
        presenter?.openNormCalories()
    }

    //func openEditBMI() {
    //    presenter?.openEditBMI()
    //}
}
