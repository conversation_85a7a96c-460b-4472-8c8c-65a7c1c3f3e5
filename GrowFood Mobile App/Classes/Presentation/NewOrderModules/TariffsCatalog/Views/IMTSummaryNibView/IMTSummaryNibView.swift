//
//  IMTSummaryNibView.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 23/05/2025.
//  Copyright © 2024 GrowFood. All rights reserved.
//

import UIKit

protocol IMTSummaryNibViewDelegate: AnyObject {
    func openMenuType(menuType: MenuTypeBmiViewModel,
                      firstMealDate: Date?,
                      firstMealSession: String?,
                      sectionId: String)
    func openInfoBmi(viewModel: MenuTypeBmiViewModel)
    func openEditWeight()
    func openNormCalories()
    func openEditBMI()
    func openAllFilters(delegate: MenuFilterSettingsModuleDelegate)
}

final class IMTSummaryNibView: NibView {

    // MARK: - Outlets
    @IBOutlet weak var stackView: UIStackView!
    @IBOutlet weak var loadingView: LoadingView!

    // MARK: - Properties
    weak var delegate: IMTSummaryNibViewDelegate?

    private var presenter: IMTSummaryNibViewPresenter?
    private var interactor: IMTSummaryNibViewInteractor?

    var isFirstTime: Bool = false
    var sectionId: String = ""
    var userHasMenuTypeGroup = false

    // MARK: - Lifecycle
    override func setupView() {
        super.setupView()
        setupVIPER()
        setupStackView()
        setupLoadingView()
        if let curUser = UserService.shared.curUser {
            userHasMenuTypeGroup = curUser.bmiFilled && !isFirstTime
        }
    }

    // MARK: - Public Methods
    func configure(menuTypeGroupIdH: String,
                   menuTypeCode: String,
                   isFirstTime: Bool,
                   firstDeliveryDate: Date? = nil,
                   firstDeliverySession: String? = nil,
                   sectionId: String? = nil) {
        self.isFirstTime = isFirstTime
        self.sectionId = sectionId ?? ""

        interactor?.configure(menuTypeGroupIdH: menuTypeGroupIdH,
                             menuTypeCode: menuTypeCode,
                             firstDeliveryDate: firstDeliveryDate,
                             firstDeliverySession: firstDeliverySession)

        loadData()
    }

    func loadData() {
        showLoading()
        presenter?.viewWillAppear()
    }

    // MARK: - Private Methods
    private func setupVIPER() {
        let presenter = IMTSummaryNibViewPresenter()
        let interactor = IMTSummaryNibViewInteractor()

        self.presenter = presenter
        self.interactor = interactor

        presenter.view = self
        presenter.interactor = interactor
        presenter.delegate = self

        interactor.presenter = presenter
    }

    private func setupStackView() {
        stackView.axis = .vertical
        stackView.spacing = 0
        stackView.alignment = .fill
        stackView.distribution = .fill
        stackView.backgroundColor = .clear
    }

    private func setupLoadingView() {
        loadingView.delegate = self
        loadingView.isHidden = true
    }

    private func showLoading() {
        loadingView.isHidden = false
        stackView.isHidden = true
    }

    private func hideLoading() {
        loadingView.isHidden = true
        stackView.isHidden = false
    }

    private func populateStackView() {
        stackView.arrangedSubviews.forEach { $0.removeFromSuperview() }

        guard let presenter = presenter else { return }

        /// Добавляем описание
        addDescriptionView()

        /// Добавляем группы
        for (groupIndex, group) in presenter.groups.enumerated() {
            addGroupHeaderView(group: group, groupIndex: groupIndex)

            for (itemIndex, _) in group.menuTypeBmiModels.enumerated() {
                addGroupItemView(group: group, groupIndex: groupIndex, itemIndex: itemIndex)
            }

            /// Добавляем разделитель между группами (кроме последней)
            if groupIndex < presenter.groups.count - 1 {
                addSeparatorView()
            }
        }
    }

    private func addDescriptionView() {
        let descriptionView = createDescriptionView()
        stackView.addArrangedSubview(descriptionView)
    }

    private func addGroupHeaderView(group: IMTSummaryGroupViewModel, groupIndex: Int) {
        let separatorView = createSeparatorView()
        stackView.addArrangedSubview(separatorView)
        
        let headerView = createGroupHeaderView(group: group, groupIndex: groupIndex)
        stackView.addArrangedSubview(headerView)
    }

    private func addGroupItemView(group: IMTSummaryGroupViewModel, groupIndex: Int, itemIndex: Int) {
        let itemView = createGroupItemView(group: group, groupIndex: groupIndex, itemIndex: itemIndex)
        stackView.addArrangedSubview(itemView)
    }

    private func addSeparatorView() {
        let separatorView = UIView()
        separatorView.backgroundColor = .clear
        separatorView.translatesAutoresizingMaskIntoConstraints = false
        separatorView.heightAnchor.constraint(equalToConstant: 8).isActive = true
        stackView.addArrangedSubview(separatorView)
    }

    // MARK: - View Creation Methods
    private func createDescriptionView() -> UIView {
        let nib = UINib(nibName: "IMTSummaryDecsriptionCollectionViewCell", bundle: nil)
        let cell = nib.instantiate(withOwner: nil, options: nil).first as! IMTSummaryDecsriptionCollectionViewCell

        cell.delegate = self
        if isFirstTime {
            cell.changeWeightView.isHidden = true
        }

        /// Настраиваем высоту
        cell.translatesAutoresizingMaskIntoConstraints = false
        cell.heightAnchor.constraint(equalToConstant: userHasMenuTypeGroup ? 140 : 64).isActive = true

        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(descriptionViewTapped))
        cell.addGestureRecognizer(tapGesture)
        cell.isUserInteractionEnabled = true

        return cell
    }
    
    private func createSeparatorView() -> UIView {
        let view = UIView()
        view.backgroundColor = ColorStyle.Black.grey4.value
        view.translatesAutoresizingMaskIntoConstraints = false
        view.heightAnchor.constraint(equalToConstant: 8).isActive = true
        return view
    }

    private func createGroupHeaderView(group: IMTSummaryGroupViewModel, groupIndex: Int) -> UIView {
        let nib = UINib(nibName: "IMTSummaryGoalHeaderCollectionViewCell", bundle: nil)
        let cell = nib.instantiate(withOwner: nil, options: nil).first as! IMTSummaryGoalHeaderCollectionViewCell

        cell.setGroupViewModel(group)

        /// Настраиваем высоту
        cell.translatesAutoresizingMaskIntoConstraints = false
        cell.heightAnchor.constraint(equalToConstant: group.heightHeader).isActive = true

        return cell
    }

    private func createGroupItemView(group: IMTSummaryGroupViewModel, groupIndex: Int, itemIndex: Int) -> UIView {
        let nib = UINib(nibName: "IMTSummaryGoalItemCollectionViewCell", bundle: nil)
        let cell = nib.instantiate(withOwner: nil, options: nil).first as! IMTSummaryGoalItemCollectionViewCell

        let indexPath = IndexPath(item: itemIndex + 1, section: groupIndex + 1) // +1 потому что section 0 - это описание
        cell.setGroup(group, at: indexPath)
        cell.delegate = self

        /// Настраиваем высоту
        cell.translatesAutoresizingMaskIntoConstraints = false
        cell.heightAnchor.constraint(equalToConstant: group.heightAt(indexPath: indexPath)).isActive = true

        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(groupItemViewTapped(_:)))
        cell.addGestureRecognizer(tapGesture)
        cell.isUserInteractionEnabled = true
        cell.tag = groupIndex * 1000 + itemIndex

        return cell
    }

    // MARK: - Gesture Handlers
    @objc private func descriptionViewTapped() {
        if let curUser = UserService.shared.curUser {
            if curUser.menuTypeGroupIdH != "" {
                presenter?.openEditWeight()
            }
        }
    }

    @objc private func groupItemViewTapped(_ gesture: UITapGestureRecognizer) {
        guard let view = gesture.view else { return }

        let groupIndex = view.tag / 1000
        let itemIndex = view.tag % 1000
        let indexPath = IndexPath(item: itemIndex + 1, section: groupIndex + 1)

        presenter?.openGoalAt(indexPath: indexPath)
    }
}

// MARK: - IMTSummaryNibViewInput
extension IMTSummaryNibView: IMTSummaryNibViewInput {
    func reloadData() {
        DispatchQueue.main.async {
            self.hideLoading()
            self.populateStackView()
        }
    }

    func showError(_ error: GFError) {
        DispatchQueue.main.async {
            self.loadingView.showError(error)
        }
    }
}

// MARK: - LoadingViewDelegate
extension IMTSummaryNibView: LoadingViewDelegate {
    func tappedReload(fromMenu: Bool) {
        loadData()
    }
}

// MARK: - IMTSummaryNibViewPresenterDelegate
extension IMTSummaryNibView: IMTSummaryNibViewPresenterDelegate {
    func openMenuType(menuType: MenuTypeBmiViewModel,
                      firstMealDate: Date?,
                      firstMealSession: String?,
                      sectionId: String) {
        delegate?.openMenuType(menuType: menuType,
                              firstMealDate: firstMealDate,
                              firstMealSession: firstMealSession,
                              sectionId: sectionId)
    }

    func openInfoBmi(viewModel: MenuTypeBmiViewModel) {
        delegate?.openInfoBmi(viewModel: viewModel)
    }

    func openEditWeight() {
        delegate?.openEditWeight()
    }

    func openNormCalories() {
        delegate?.openNormCalories()
    }

    func openEditBMI() {
        delegate?.openEditBMI()
    }

    func openAllFilters(delegate: MenuFilterSettingsModuleDelegate) {
        self.delegate?.openAllFilters(delegate: delegate)
    }
}

// MARK: - IMTSummaryGoalItemCollectionViewCellDelegate
extension IMTSummaryNibView: IMTSummaryGoalItemCollectionViewCellDelegate {
    func selectedAt(indexPath: IndexPath) {
        presenter?.openGoalAt(indexPath: indexPath)
    }

    func tappedInfoAt(indexPath: IndexPath) {
        presenter?.openInfoAt(indexPath: indexPath)
    }
}

// MARK: - IMTSummaryDecsriptionCollectionViewCellDelegate
extension IMTSummaryNibView: IMTSummaryDecsriptionCollectionViewCellDelegate {
    func openNormalCalories() {
        presenter?.openNormCalories()
    }
}
