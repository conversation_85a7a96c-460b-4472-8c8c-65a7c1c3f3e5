<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="23504" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="23506"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="EmptyBMIView" customModule="GrowFood_Mobile_App" customModuleProvider="target"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" id="oRw-j0-ag4">
            <rect key="frame" x="0.0" y="0.0" width="393" height="718"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="dn3-hI-bZy">
                    <rect key="frame" x="16" y="0.0" width="361" height="552.33333333333337"/>
                    <subviews>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="emptyBMI" translatesAutoresizingMaskIntoConstraints="NO" id="ff1-cb-fTQ">
                            <rect key="frame" x="16" y="16" width="329" height="280"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="280" id="2Bn-Ur-Z5F"/>
                            </constraints>
                        </imageView>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="kFj-Rp-sKf" customClass="GFLabelB1Regular" customModule="GrowFood_Mobile_App" customModuleProvider="target">
                            <rect key="frame" x="26" y="318" width="309" height="142.33333333333337"/>
                            <string key="text">Соберем ваш персональный план питания исходя из ваших параметров, и подскажем что лучше есть чтобы быстрее достичь свою цель идеально подходит для тех кто хочет начать правильно питаться и достигать своих целей</string>
                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                            <nil key="textColor"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <button opaque="NO" contentMode="scaleToFill" semanticContentAttribute="forceRightToLeft" contentHorizontalAlignment="center" contentVerticalAlignment="center" adjustsImageWhenHighlighted="NO" adjustsImageWhenDisabled="NO" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="mCo-VL-ULK" customClass="GFButton" customModule="GrowFood_Mobile_App" customModuleProvider="target">
                            <rect key="frame" x="16" y="492.33333333333326" width="329" height="44"/>
                            <color key="backgroundColor" name="green-main"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="44" id="Hpa-Cp-EL8"/>
                            </constraints>
                            <state key="normal">
                                <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            </state>
                            <userDefinedRuntimeAttributes>
                                <userDefinedRuntimeAttribute type="string" keyPath="titleText" value="ВВЕСТИ ДАННЫЕ"/>
                                <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                    <real key="value" value="14"/>
                                </userDefinedRuntimeAttribute>
                            </userDefinedRuntimeAttributes>
                            <connections>
                                <action selector="openBmiAction" destination="-1" eventType="touchUpInside" id="9E9-hA-bb4"/>
                            </connections>
                        </button>
                    </subviews>
                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstAttribute="trailing" secondItem="ff1-cb-fTQ" secondAttribute="trailing" constant="16" id="2Ac-Lm-zdM"/>
                        <constraint firstItem="ff1-cb-fTQ" firstAttribute="leading" secondItem="dn3-hI-bZy" secondAttribute="leading" constant="16" id="7Be-Qg-cYb"/>
                        <constraint firstAttribute="trailing" secondItem="kFj-Rp-sKf" secondAttribute="trailing" constant="26" id="7d3-8a-mmt"/>
                        <constraint firstItem="mCo-VL-ULK" firstAttribute="leading" secondItem="dn3-hI-bZy" secondAttribute="leading" constant="16" id="EVR-ev-P1B"/>
                        <constraint firstItem="mCo-VL-ULK" firstAttribute="top" secondItem="kFj-Rp-sKf" secondAttribute="bottom" constant="32" id="Gex-hS-c5L"/>
                        <constraint firstItem="ff1-cb-fTQ" firstAttribute="top" secondItem="dn3-hI-bZy" secondAttribute="top" constant="16" id="VdQ-3G-XBw"/>
                        <constraint firstItem="kFj-Rp-sKf" firstAttribute="top" secondItem="ff1-cb-fTQ" secondAttribute="bottom" constant="22" id="fnF-A9-Lfk"/>
                        <constraint firstAttribute="bottom" secondItem="mCo-VL-ULK" secondAttribute="bottom" constant="16" id="kpN-WF-420"/>
                        <constraint firstItem="kFj-Rp-sKf" firstAttribute="leading" secondItem="dn3-hI-bZy" secondAttribute="leading" constant="26" id="oYD-jJ-ynC"/>
                        <constraint firstAttribute="trailing" secondItem="mCo-VL-ULK" secondAttribute="trailing" constant="16" id="z92-9V-drh"/>
                    </constraints>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                            <real key="value" value="14"/>
                        </userDefinedRuntimeAttribute>
                    </userDefinedRuntimeAttributes>
                </view>
            </subviews>
            <color key="backgroundColor" name="black-grey-6"/>
            <constraints>
                <constraint firstItem="dn3-hI-bZy" firstAttribute="leading" secondItem="oRw-j0-ag4" secondAttribute="leading" constant="16" id="Jhj-CX-ESe"/>
                <constraint firstItem="dn3-hI-bZy" firstAttribute="top" secondItem="oRw-j0-ag4" secondAttribute="top" id="Z0a-9x-oMm"/>
                <constraint firstAttribute="trailing" secondItem="dn3-hI-bZy" secondAttribute="trailing" constant="16" id="sVC-U6-j9t"/>
                <constraint firstAttribute="bottom" relation="greaterThanOrEqual" secondItem="dn3-hI-bZy" secondAttribute="bottom" constant="16" id="xxg-KY-0GA"/>
            </constraints>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <point key="canvasLocation" x="232.82442748091603" y="-66.197183098591552"/>
        </view>
    </objects>
    <resources>
        <image name="emptyBMI" width="324" height="324"/>
        <namedColor name="black-grey-6">
            <color red="0.93725490196078431" green="0.93725490196078431" blue="0.93725490196078431" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="green-main">
            <color red="0.0" green="0.72549019607843135" blue="0.13725490196078433" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
    </resources>
</document>
