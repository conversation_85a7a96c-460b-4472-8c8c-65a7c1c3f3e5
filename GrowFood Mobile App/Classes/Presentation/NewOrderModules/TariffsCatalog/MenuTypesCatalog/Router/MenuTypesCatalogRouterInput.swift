//
//  MenuTypesCatalogRouterInput.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 18/10/2022.
//  Copyright © 2022 GrowFood. All rights reserved.
//

protocol MenuTypesCatalogRouterInput {
    func popToRootViewController(animated: Bool)
    func openMenuTypesWith(cardViewModel: MenuSectionCardDropdownViewModel,
                           delegate: MenuTypesForCardModuleDelegate?)
    func openMenuType(menuType: MenuTypeCardViewModel,
                      menuCatalogSectionId: String,
                      familyId: String?,
                      defaultStartDate: Date?,
                      defaultStartSession: DeliveryDay.DeliveryType?,
                      menuTypesDelegate: MenuTypesCatalogModuleDelegate?,
					  fromLink: Bool)
    func openBmi(bmiViewModel: GoalBMIViewModel,
                 cardViewModel: MenuSectionCardDropdownViewModel,
                 delegate: (BMIMotiovationMenuTypesModuleDelegate & MenuTypesForCardModuleDelegate)?)
    func openBmiSettings(model: GoalViewModel)
    func infoOpen(_ menuType: MenuTypeCardViewModel,
                  viewModels: [MenuTypeCardViewModel],
                  inGroupWithId groupId: String,
                  delegate: MenuTypeInfoModuleDelegate)
    func openAllFilters(delegate: MenuFilterSettingsModuleDelegate)
    
    /// Для IMTSummaryNibViewDelegate
    func openMenuType(menuType: MenuTypeBmiViewModel,
                      firstMealDate: Date?,
                      firstMealSession: String?,
                      sectionId: String)
    func openEditBMI()
    func openNormCalories()
    func openEditWeight()
    func openInfoBmi(viewModel: MenuTypeBmiViewModel)
}
