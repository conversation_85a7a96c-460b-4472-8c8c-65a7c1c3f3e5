//
//  MenuTypesCatalogRouter.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 18/10/2022.
//  Copyright © 2022 GrowFood. All rights reserved.
//

import UIKit

final class MenuTypesCatalogRouter: MenuTypesCatalogRouterInput {
	// MARK: VIPER Stack Properties

    weak var view: (MenuTypesCatalogViewInput & UIViewController)!

	// MARK: - MenuTypesCatalogRouterInput
    
    var isMenuTypeSelecting: Bool = false
    var restrictedMenuTypes: [Int] = []
    
    func popToRootViewController(animated: Bool) {
        view.navigationController?.popToRootViewController(animated: animated)
    }
    
    func infoOpen(_ menuType: MenuTypeCardViewModel,
                  viewModels: [MenuTypeCardViewModel],
                  inGroupWithId groupId: String,
                  delegate: MenuTypeInfoModuleDelegate) {
        let infoModule = MenuTypeInfoConfigurator.configure(viewModels: viewModels,
                                                            defaultViewModel: menuType,
                                                            sectionId: groupId,
                                                            delegate: delegate)
        view.present(infoModule, animated: true)
    }
    
    
    
    func openMenuTypesWith(cardViewModel: MenuSectionCardDropdownViewModel,
                           delegate: MenuTypesForCardModuleDelegate?) {
        let config = MenuTypesForCardConfigurator.configure(
            catalogCardId: cardViewModel.id,
            isMenuTypeSelecting: isMenuTypeSelecting,
            restrictedMenuTypes: restrictedMenuTypes,
            delegate: delegate)
        view.present(config, animated: true)
    }
    
    func openBmi(bmiViewModel: GoalBMIViewModel,
                 cardViewModel: MenuSectionCardDropdownViewModel,
                 delegate: (BMIMotiovationMenuTypesModuleDelegate & MenuTypesForCardModuleDelegate)?) {
        if UserService.shared.curUser?.bmiFilled == true {
            if UserService.shared.curUser?.purposeWeight ?? 0 == 0 {
                let model = GoalViewModel(bmiViewModel: bmiViewModel,
                                          catalogId: cardViewModel.id)
                let module = IMTFlowContainerModuleConfigurator.configure(
                    goalModel: model,
                    firstDeliveryDate: nil,
                    firstDeliverySession: nil,
                    isFirstTime: false,
                    imtEdit: false,
                    removeMotiovation: true)
                let navVC = GFURLRouter.shared.newRootTabBar?.viewController?.navigationController
                navVC?.pushViewController(module,
                                          animated: true)
            }
            else {
                let module = IMTSummaryModuleConfigurator.configure(
                    menuTypeGroupIdH: "",
                    menuTypeCode: "",
                    isFirstTime: false,
                    firstDeliveryDate: nil,
                    firstDeliverySession: nil,
                    sectionId: cardViewModel.id)
                let navVC = GFURLRouter.shared.newRootTabBar?.viewController?.navigationController
                navVC?.pushViewController(module,
                                          animated: true)
            }
        }
        else {
            let module = BMIMotiovationMenuTypesConfigurator.configure(
                cardViewModel: cardViewModel,
                bmiViewModel: bmiViewModel,
                delegate: delegate)
            let navVC = UINavigationController(rootViewController: module)
            navVC.setNavigationBarHidden(true,
                                         animated: false)
            view.present(navVC, animated: true)
        }

    }
    
    func openAllFilters(delegate: MenuFilterSettingsModuleDelegate) {
        let module = MenuFilterSettingsV2Configurator.configure(delegate: delegate,
                                                                baseMenu: nil,
                                                                selectedFilters: [],
                                                                orderIdH: nil,
                                                                hasUserCustom: false,
                                                                source: .proposeFiltersChange)
        let isPresented = (view.navigationController?.presentingViewController != nil)
        let navVC = isPresented ? view.navigationController : GFURLRouter.shared.newRootTabBar?.viewController?.navigationController
        navVC?.pushViewController(module,
                                  animated: true)
    }
    
    func openMenuType(menuType: MenuTypeCardViewModel,
                      menuCatalogSectionId: String,
                      familyId: String?,
                      defaultStartDate: Date?,
                      defaultStartSession: DeliveryDay.DeliveryType?,
                      menuTypesDelegate: MenuTypesCatalogModuleDelegate?,
					  fromLink: Bool = false) {
        let module = NewOrderConfigurator.configure(menuType: menuType.menuType,
                                                    sectionId: menuCatalogSectionId,
                                                    tariffCode: view.tariffCode ?? "",
                                                    cityId: UserService.shared.cityId,
                                                    isFromBMI: false,
                                                    defaultStartDate: defaultStartDate,
                                                    defaultStartSession: defaultStartSession,
                                                    familyId: familyId,
                                                    isLongation: false,
                                                    isMenuTypeSelecting: isMenuTypeSelecting,
                                                    menuTypesDelegate: menuTypesDelegate,
													fromLink: fromLink)
        let isPresented = (view.navigationController?.presentingViewController != nil)
        let navVC = isPresented ? view.navigationController : GFURLRouter.shared.newRootTabBar?.viewController?.navigationController
        navVC?.pushViewController(module,
                                  animated: true)
    }
    
    func openBmiSettings(model: GoalViewModel) {
        let module = IMTFlowContainerModuleConfigurator.configure(goalModel: model,
                                                                  firstDeliveryDate: nil,
                                                                  firstDeliverySession: nil,
                                                                  isFirstTime: true,
                                                                  imtEdit: false,
                                                                  removeMotiovation: true)
        let navVC = GFURLRouter.shared.newRootTabBar?.viewController?.navigationController
        navVC?.pushViewController(module,
                                  animated: true)
    }
    
    
    func openMenuType(menuType: MenuTypeBmiViewModel,
                      firstMealDate: Date?,
                      firstMealSession: String?,
                      sectionId: String) {
        var defaultSession: DeliveryDay.DeliveryType? = nil
        if let firstMealSession {
            defaultSession = DeliveryDay.DeliveryType.init(rawValue: firstMealSession) ?? .evening
        }
        let module = NewOrderConfigurator.configure(
            menuType: menuType.menuType,
            sectionId: sectionId,
            cityId: menuType.cityId,
            isFromBMI: true,
            defaultStartDate: firstMealDate,
            defaultStartSession: defaultSession,
            familyId: nil,
            isLongation: false)
        view.navigationController?.pushViewController(module,
                                                      animated: true)
    }
    
    func openEditBMI() {
        let module = IMTFlowContainerModuleConfigurator.configure(isFromProfile: false)
        view.navigationController?.pushViewController(module,
                                                      animated: true)
    }
    
    func openNormCalories() {
        let module = IMTFlowContainerModuleConfigurator.configureSummary()
        view.navigationController?.pushViewController(module,
                                                      animated: true)
    }
    
    func openEditWeight() {
        let module = IMTFlowContainerModuleConfigurator.configureEditWeight(isPresented: false)
        view.navigationController?.pushViewController(module,
                                                      animated: true)
    }
    
    func openInfoBmi(viewModel: MenuTypeBmiViewModel) {
        let infoModle = IMTGoalInfoModuleConfigurator.configure(viewModel: viewModel)
        view.navigationController?.pushViewController(infoModle,
                                                      animated: true)
    }
}
