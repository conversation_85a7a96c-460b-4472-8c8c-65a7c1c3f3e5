<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="23504" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES" initialViewController="h3k-bK-TNO">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="23506"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--Menu Types Catalog View Controller-->
        <scene sceneID="PlN-iW-H7B">
            <objects>
                <viewController id="h3k-bK-TNO" customClass="MenuTypesCatalogViewController" customModule="GrowFood_Mobile_App" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="A8X-aG-ZDh">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <scrollView multipleTouchEnabled="YES" contentMode="scaleToFill" ambiguous="YES" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" delaysContentTouches="NO" translatesAutoresizingMaskIntoConstraints="NO" id="iY4-vY-dUs" customClass="UIButtonScrollView" customModule="GrowFood_Mobile_App" customModuleProvider="target">
                                <rect key="frame" x="0.0" y="0.0" width="393" height="818"/>
                                <subviews>
                                    <stackView opaque="NO" contentMode="scaleToFill" ambiguous="YES" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="Wn5-ga-ykP">
                                        <rect key="frame" x="0.0" y="0.0" width="393" height="848"/>
                                        <subviews>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="1XO-k9-RNt">
                                                <rect key="frame" x="0.0" y="0.0" width="393" height="86"/>
                                                <subviews>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="JxN-Eb-dVb" customClass="GFSegmentedControl" customModule="GrowFood_Mobile_App" customModuleProvider="target">
                                                        <rect key="frame" x="16" y="0.0" width="361" height="70"/>
                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    </view>
                                                </subviews>
                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstItem="JxN-Eb-dVb" firstAttribute="top" secondItem="1XO-k9-RNt" secondAttribute="top" id="9V6-AK-S8r"/>
                                                    <constraint firstAttribute="bottom" secondItem="JxN-Eb-dVb" secondAttribute="bottom" constant="16" id="gsq-bG-tv4"/>
                                                    <constraint firstAttribute="trailing" secondItem="JxN-Eb-dVb" secondAttribute="trailing" constant="16" id="jE3-5X-iOz"/>
                                                    <constraint firstItem="JxN-Eb-dVb" firstAttribute="leading" secondItem="1XO-k9-RNt" secondAttribute="leading" constant="16" id="mAr-pc-9TF"/>
                                                    <constraint firstAttribute="height" constant="86" id="v0z-Ad-B28"/>
                                                </constraints>
                                            </view>
                                            <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="mBB-g4-g6p" customClass="TariffsCatalogBannersView" customModule="GrowFood_Mobile_App" customModuleProvider="target">
                                                <rect key="frame" x="0.0" y="86" width="393" height="0.0"/>
                                                <color key="backgroundColor" systemColor="systemBlueColor"/>
                                            </view>
                                            <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="7NP-RR-tGG">
                                                <rect key="frame" x="0.0" y="86" width="393" height="148"/>
                                                <subviews>
                                                    <view clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="JBB-DE-gFa">
                                                        <rect key="frame" x="16" y="0.0" width="361" height="132"/>
                                                        <subviews>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="План похудения" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="1MK-hT-3fd" customClass="GFLabelH4" customModule="GrowFood_Mobile_App" customModuleProvider="target">
                                                                <rect key="frame" x="16" y="11.999999999999998" width="129" height="20.333333333333329"/>
                                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                                <nil key="textColor"/>
                                                                <nil key="highlightedColor"/>
                                                                <userDefinedRuntimeAttributes>
                                                                    <userDefinedRuntimeAttribute type="color" keyPath="textColor">
                                                                        <color key="value" name="black-main"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                </userDefinedRuntimeAttributes>
                                                            </label>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="opx-16-coT" customClass="GFLabelB2Regular" customModule="GrowFood_Mobile_App" customModuleProvider="target">
                                                                <rect key="frame" x="16" y="36.333333333333329" width="329" height="40.666666666666671"/>
                                                                <string key="text">Составили персональный план
похудения под ваши параметры</string>
                                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                                <nil key="textColor"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="bmi_ves" translatesAutoresizingMaskIntoConstraints="NO" id="ra4-dG-zUS">
                                                                <rect key="frame" x="159" y="10" width="240" height="122"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="width" constant="240" id="Aen-TA-tBB"/>
                                                                </constraints>
                                                            </imageView>
                                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Qmm-Jr-8RQ" customClass="GFButtonB2Medium" customModule="GrowFood_Mobile_App" customModuleProvider="target">
                                                                <rect key="frame" x="16" y="89" width="142" height="32"/>
                                                                <accessibility key="accessibilityConfiguration" identifier="bmi_button"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="32" id="Lio-En-pcQ"/>
                                                                    <constraint firstAttribute="width" constant="142" id="Vlf-gj-79K"/>
                                                                </constraints>
                                                                <state key="normal" title="Button"/>
                                                                <buttonConfiguration key="configuration" style="plain" title="Посмотреть"/>
                                                                <userDefinedRuntimeAttributes>
                                                                    <userDefinedRuntimeAttribute type="color" keyPath="normalBackgroundColor">
                                                                        <color key="value" name="darkGreen"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                    <userDefinedRuntimeAttribute type="color" keyPath="highlightedBackgroundColor">
                                                                        <color key="value" name="green-shadow"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                    <userDefinedRuntimeAttribute type="color" keyPath="normalTintColor">
                                                                        <color key="value" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                    <userDefinedRuntimeAttribute type="color" keyPath="highlightedTintColor">
                                                                        <color key="value" name="black-grey-4"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                    <userDefinedRuntimeAttribute type="string" keyPath="titleText" value="ПОСМОТРЕТЬ"/>
                                                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                                        <real key="value" value="16"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                </userDefinedRuntimeAttributes>
                                                                <connections>
                                                                    <action selector="openBmi" destination="h3k-bK-TNO" eventType="touchUpInside" id="nDo-ay-6Yk"/>
                                                                </connections>
                                                            </button>
                                                        </subviews>
                                                        <color key="backgroundColor" name="saladGreen"/>
                                                        <constraints>
                                                            <constraint firstAttribute="trailing" secondItem="opx-16-coT" secondAttribute="trailing" constant="16" id="08N-Sy-wvO"/>
                                                            <constraint firstItem="ra4-dG-zUS" firstAttribute="top" secondItem="JBB-DE-gFa" secondAttribute="top" constant="10" id="FwY-fq-wGW"/>
                                                            <constraint firstItem="1MK-hT-3fd" firstAttribute="leading" secondItem="JBB-DE-gFa" secondAttribute="leading" constant="16" id="INE-mO-wUg"/>
                                                            <constraint firstItem="Qmm-Jr-8RQ" firstAttribute="leading" secondItem="JBB-DE-gFa" secondAttribute="leading" constant="16" id="MaL-6g-rvx"/>
                                                            <constraint firstItem="1MK-hT-3fd" firstAttribute="top" secondItem="JBB-DE-gFa" secondAttribute="top" constant="12" id="P69-xV-ooM"/>
                                                            <constraint firstAttribute="trailing" secondItem="ra4-dG-zUS" secondAttribute="trailing" constant="-38" id="Qwf-Uy-0ra"/>
                                                            <constraint firstItem="opx-16-coT" firstAttribute="top" secondItem="1MK-hT-3fd" secondAttribute="bottom" constant="4" id="S94-EC-ywE"/>
                                                            <constraint firstItem="Qmm-Jr-8RQ" firstAttribute="top" secondItem="opx-16-coT" secondAttribute="bottom" constant="12" id="ZYy-9Y-06X"/>
                                                            <constraint firstAttribute="bottom" secondItem="ra4-dG-zUS" secondAttribute="bottom" id="emT-gP-xWe"/>
                                                            <constraint firstItem="opx-16-coT" firstAttribute="leading" secondItem="JBB-DE-gFa" secondAttribute="leading" constant="16" id="kta-hU-bOR"/>
                                                            <constraint firstAttribute="height" constant="132" id="wiH-kB-kXQ"/>
                                                        </constraints>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                                <real key="value" value="12"/>
                                                            </userDefinedRuntimeAttribute>
                                                        </userDefinedRuntimeAttributes>
                                                    </view>
                                                </subviews>
                                                <color key="backgroundColor" name="black-grey-6"/>
                                                <accessibility key="accessibilityConfiguration" identifier="bmi_view"/>
                                                <constraints>
                                                    <constraint firstItem="JBB-DE-gFa" firstAttribute="leading" secondItem="7NP-RR-tGG" secondAttribute="leading" constant="16" id="Aht-jc-2eJ"/>
                                                    <constraint firstAttribute="trailing" secondItem="JBB-DE-gFa" secondAttribute="trailing" constant="16" id="K0Z-86-5UA"/>
                                                    <constraint firstAttribute="bottom" secondItem="JBB-DE-gFa" secondAttribute="bottom" constant="16" id="PAe-rT-R0F"/>
                                                    <constraint firstItem="JBB-DE-gFa" firstAttribute="top" secondItem="7NP-RR-tGG" secondAttribute="top" id="ar4-Eb-YF7"/>
                                                </constraints>
                                            </view>
                                            <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="brj-Dh-b0b" customClass="TariffsCatalogCardsView" customModule="GrowFood_Mobile_App" customModuleProvider="target">
                                                <rect key="frame" x="0.0" y="86" width="393" height="0.0"/>
                                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                            </view>
                                            <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="U2w-qw-ZVO" customClass="MenuTypesListSelectSortView" customModule="GrowFood_Mobile_App" customModuleProvider="target">
                                                <rect key="frame" x="0.0" y="86" width="393" height="0.0"/>
                                                <color key="backgroundColor" name="black-grey-6"/>
                                            </view>
                                            <view clipsSubviews="YES" contentMode="scaleToFill" ambiguous="YES" translatesAutoresizingMaskIntoConstraints="NO" id="KNd-eM-NRw" customClass="MenuTypesListView" customModule="GrowFood_Mobile_App" customModuleProvider="target">
                                                <rect key="frame" x="0.0" y="86" width="393" height="762"/>
                                                <color key="backgroundColor" name="black-grey-6"/>
                                            </view>
                                        </subviews>
                                        <color key="backgroundColor" name="black-grey-6"/>
                                        <edgeInsets key="layoutMargins" top="0.0" left="0.0" bottom="0.0" right="0.0"/>
                                    </stackView>
                                    <view contentMode="scaleToFill" ambiguous="YES" translatesAutoresizingMaskIntoConstraints="NO" id="flZ-g3-q7d" customClass="EmptyBMIView" customModule="GrowFood_Mobile_App" customModuleProvider="target">
                                        <rect key="frame" x="0.0" y="98" width="393" height="750"/>
                                        <color key="backgroundColor" name="black-grey-6"/>
                                    </view>
                                    <view contentMode="scaleToFill" ambiguous="YES" translatesAutoresizingMaskIntoConstraints="NO" id="cCs-gr-Olu" customClass="IMTSummaryNibView" customModule="GrowFood_Mobile_App" customModuleProvider="target">
                                        <rect key="frame" x="0.0" y="98" width="393" height="750"/>
                                        <color key="backgroundColor" name="black-grey-6"/>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" name="black-grey-6"/>
                                <constraints>
                                    <constraint firstAttribute="trailing" secondItem="cCs-gr-Olu" secondAttribute="trailing" id="02X-yh-O3f"/>
                                    <constraint firstItem="Wn5-ga-ykP" firstAttribute="top" secondItem="iY4-vY-dUs" secondAttribute="top" id="4xH-yl-YrJ"/>
                                    <constraint firstItem="cCs-gr-Olu" firstAttribute="top" secondItem="Wn5-ga-ykP" secondAttribute="top" constant="88" id="6CT-Mu-6ab"/>
                                    <constraint firstItem="Wn5-ga-ykP" firstAttribute="width" secondItem="iY4-vY-dUs" secondAttribute="width" id="H24-zS-joC"/>
                                    <constraint firstAttribute="bottom" secondItem="Wn5-ga-ykP" secondAttribute="bottom" id="HMs-l1-xbb"/>
                                    <constraint firstAttribute="bottom" secondItem="cCs-gr-Olu" secondAttribute="bottom" id="Hbr-fN-qXA"/>
                                    <constraint firstItem="flZ-g3-q7d" firstAttribute="leading" secondItem="iY4-vY-dUs" secondAttribute="leading" id="LH2-1s-e7X"/>
                                    <constraint firstItem="Wn5-ga-ykP" firstAttribute="leading" secondItem="iY4-vY-dUs" secondAttribute="leading" id="Nle-hW-Oat"/>
                                    <constraint firstAttribute="bottom" secondItem="flZ-g3-q7d" secondAttribute="bottom" id="RTP-Pm-ioj"/>
                                    <constraint firstItem="flZ-g3-q7d" firstAttribute="top" secondItem="Wn5-ga-ykP" secondAttribute="top" constant="88" id="jAZ-6Y-zfw"/>
                                    <constraint firstAttribute="trailing" secondItem="flZ-g3-q7d" secondAttribute="trailing" id="rwy-Gf-pAE"/>
                                    <constraint firstAttribute="trailing" secondItem="Wn5-ga-ykP" secondAttribute="trailing" id="t90-ER-04N"/>
                                    <constraint firstItem="cCs-gr-Olu" firstAttribute="leading" secondItem="iY4-vY-dUs" secondAttribute="leading" id="zKl-47-Fa6"/>
                                </constraints>
                            </scrollView>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="k1Z-BF-cDR">
                                <rect key="frame" x="0.0" y="852" width="393" height="0.0"/>
                                <color key="backgroundColor" name="black-grey-6"/>
                                <constraints>
                                    <constraint firstAttribute="height" id="TYI-xo-T9Z"/>
                                </constraints>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="l2b-Gs-rEa">
                                <rect key="frame" x="0.0" y="0.0" width="393" height="0.0"/>
                                <subviews>
                                    <visualEffectView opaque="NO" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="iwu-Ob-6Me">
                                        <rect key="frame" x="0.0" y="0.0" width="393" height="0.0"/>
                                        <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" insetsLayoutMarginsFromSafeArea="NO" id="Ryv-Gs-bg1">
                                            <rect key="frame" x="0.0" y="0.0" width="393" height="0.0"/>
                                            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                        </view>
                                        <blurEffect style="regular"/>
                                    </visualEffectView>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="iwu-Ob-6Me" firstAttribute="leading" secondItem="l2b-Gs-rEa" secondAttribute="leading" id="Hmb-Z7-cVc"/>
                                    <constraint firstAttribute="trailing" secondItem="iwu-Ob-6Me" secondAttribute="trailing" id="ThI-Dr-IgZ"/>
                                    <constraint firstAttribute="height" id="qFQ-5s-ZlJ"/>
                                    <constraint firstAttribute="bottom" secondItem="iwu-Ob-6Me" secondAttribute="bottom" id="tha-Zc-GMm"/>
                                    <constraint firstItem="iwu-Ob-6Me" firstAttribute="top" secondItem="l2b-Gs-rEa" secondAttribute="top" id="wdR-I8-dpx"/>
                                </constraints>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="8Zj-ar-Kyb" customClass="LoadingView" customModule="GrowFood_Mobile_App" customModuleProvider="target">
                                <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                                <color key="backgroundColor" name="black-grey-6"/>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="7VX-sQ-2SE"/>
                        <color key="backgroundColor" name="black-grey-6"/>
                        <constraints>
                            <constraint firstAttribute="bottom" secondItem="8Zj-ar-Kyb" secondAttribute="bottom" id="9Yn-g4-hMH"/>
                            <constraint firstItem="l2b-Gs-rEa" firstAttribute="top" secondItem="A8X-aG-ZDh" secondAttribute="top" id="EUl-wr-1g1"/>
                            <constraint firstAttribute="trailing" secondItem="l2b-Gs-rEa" secondAttribute="trailing" id="IWl-OT-fcA"/>
                            <constraint firstItem="7VX-sQ-2SE" firstAttribute="trailing" secondItem="k1Z-BF-cDR" secondAttribute="trailing" id="T6p-KI-k5Y"/>
                            <constraint firstItem="7VX-sQ-2SE" firstAttribute="bottom" secondItem="iY4-vY-dUs" secondAttribute="bottom" id="TUi-7k-bKC"/>
                            <constraint firstAttribute="bottom" secondItem="k1Z-BF-cDR" secondAttribute="bottom" id="WPZ-wN-H8w"/>
                            <constraint firstItem="iY4-vY-dUs" firstAttribute="top" secondItem="A8X-aG-ZDh" secondAttribute="top" id="XJT-9H-sYR"/>
                            <constraint firstItem="7VX-sQ-2SE" firstAttribute="trailing" secondItem="iY4-vY-dUs" secondAttribute="trailing" id="cFj-eq-SBR"/>
                            <constraint firstItem="l2b-Gs-rEa" firstAttribute="leading" secondItem="A8X-aG-ZDh" secondAttribute="leading" id="hMJ-cQ-GIc"/>
                            <constraint firstItem="k1Z-BF-cDR" firstAttribute="leading" secondItem="7VX-sQ-2SE" secondAttribute="leading" id="iqA-fB-Bg0"/>
                            <constraint firstItem="8Zj-ar-Kyb" firstAttribute="top" secondItem="A8X-aG-ZDh" secondAttribute="top" id="jwM-8M-zpb"/>
                            <constraint firstItem="8Zj-ar-Kyb" firstAttribute="leading" secondItem="7VX-sQ-2SE" secondAttribute="leading" id="o4t-Cc-tSW"/>
                            <constraint firstItem="iY4-vY-dUs" firstAttribute="leading" secondItem="7VX-sQ-2SE" secondAttribute="leading" id="qkL-hJ-Lo5"/>
                            <constraint firstItem="7VX-sQ-2SE" firstAttribute="trailing" secondItem="8Zj-ar-Kyb" secondAttribute="trailing" id="swy-AD-9hX"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="bannersView" destination="mBB-g4-g6p" id="P4R-dA-h4b"/>
                        <outlet property="blurView" destination="l2b-Gs-rEa" id="mLr-jG-28I"/>
                        <outlet property="bmiView" destination="7NP-RR-tGG" id="EZX-xN-fTt"/>
                        <outlet property="bottomBackgroundViewHeight" destination="TYI-xo-T9Z" id="AvY-sN-f3l"/>
                        <outlet property="cardsView" destination="brj-Dh-b0b" id="7Lo-s4-Z4O"/>
                        <outlet property="emptyBMIView" destination="flZ-g3-q7d" id="IEE-JW-JHE"/>
                        <outlet property="imtSummaryView" destination="cCs-gr-Olu" id="3xD-st-GeP"/>
                        <outlet property="loadingView" destination="8Zj-ar-Kyb" id="Luo-nR-5su"/>
                        <outlet property="menuTypesListView" destination="KNd-eM-NRw" id="Mel-v5-oYO"/>
                        <outlet property="menuTypesSortView" destination="U2w-qw-ZVO" id="FtW-ft-RHy"/>
                        <outlet property="scrollView" destination="iY4-vY-dUs" id="XZf-Zw-0rb"/>
                        <outlet property="selectionSegmentedControl" destination="JxN-Eb-dVb" id="DIS-Nt-avo"/>
                        <outlet property="selectionSegmentedControlView" destination="1XO-k9-RNt" id="DCT-Ia-kEi"/>
                        <outlet property="stackView" destination="Wn5-ga-ykP" id="Dy9-J9-XGE"/>
                        <outlet property="statusBarBackgroundHeight" destination="qFQ-5s-ZlJ" id="LU9-AZ-RxU"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="psv-Wb-mBc" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="573.91304347826087" y="-231.02678571428569"/>
        </scene>
    </scenes>
    <resources>
        <image name="bmi_ves" width="513" height="372"/>
        <namedColor name="black-grey-4">
            <color red="0.96078431372549022" green="0.96078431372549022" blue="0.96078431372549022" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="black-grey-6">
            <color red="0.93725490196078431" green="0.93725490196078431" blue="0.93725490196078431" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="black-main">
            <color red="0.050980392156862744" green="0.050980392156862744" blue="0.050980392156862744" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="darkGreen">
            <color red="0.059000000357627869" green="0.25099998712539673" blue="0.16099999845027924" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="green-shadow">
            <color red="0.058823529411764705" green="0.25490196078431371" blue="0.15686274509803921" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="saladGreen">
            <color red="0.77999997138977051" green="0.92199999094009399" blue="0.12200000137090683" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
        <systemColor name="systemBlueColor">
            <color red="0.0" green="0.47843137250000001" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
    </resources>
</document>
