//
//  MenuTypesCatalogViewController.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 18/10/2022.
//  Copyright © 2022 GrowFood. All rights reserved.
//

import UIKit
import SkeletonView

final class MenuTypesCatalogViewController: GFViewController {

	// MARK: Outlets
    @IBOutlet weak var selectionSegmentedControlView: UIView!
    @IBOutlet weak var selectionSegmentedControl: GFSegmentedControl!
    @IBOutlet weak var bmiView: UIView!
    @IBOutlet weak var blurView: UIView!
    @IBOutlet weak var bottomBackgroundViewHeight: NSLayoutConstraint!
    @IBOutlet weak var menuTypesListView: MenuTypesListView!
    @IBOutlet weak var menuTypesSortView: MenuTypesListSelectSortView!
    @IBOutlet weak var cardsView: TariffsCatalogCardsView!
    @IBOutlet weak var loadingView: LoadingView!
    @IBOutlet weak var statusBarBackgroundHeight: NSLayoutConstraint!
    @IBOutlet weak var stackView: UIStackView!
    @IBOutlet weak var scrollView: UIButtonScrollView!
    @IBOutlet weak var bannersView: TariffsCatalogBannersView!
    @IBOutlet weak var emptyBMIView: EmptyBMIView!
    @IBOutlet weak var imtSummaryView: IMTSummaryNibView!

    // Новый IMT Summary View
   // private var imtSummaryView: IMTSummaryNibView?
    var tariffCode: String? = nil

    var presenter: MenuTypesCatalogViewOutput?

    // MARK: Local Properties
    lazy var safeAreaTopHeight: CGFloat = {
        if self.navigationController?.presentingViewController == nil {
            return UIWindow.key?.safeAreaInsets.top ?? 0
        }
        else {
            return 0
        }
    }()
//    var selectsortView = GFDropDownAlert()
    private var isInit: Bool = true

    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupDelegates()
        setupScrollView()
        setupSegmentedControl()
        setupIMTSummaryView()
        loadData()
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        blurView.setHidden(true)

        setupNavigationBar()

        if !isInit {
            if let curSortModel = menuTypesSortView.currentSortModel {
                presenter?.selectedCardWith(cardViewModel: curSortModel)
            }
        }
        isInit = false
        sendScreenName(name: "menu_selection menu",
                       params: ["tariff": tariffCode ?? ""])
    }

    private func setupDelegates() {
        loadingView.delegate = self
        menuTypesSortView.delegate = self
        cardsView.delegate = self
        menuTypesListView.delegate = self
        bannersView.delegate = self
        emptyBMIView.delegate = self
    }

    func loadData() {
        showSkeleton()
        self.loadingView.isHidden = true
        presenter?.viewDidLoad()
    }

    fileprivate func showSkeleton() {
        self.cardsView.showCardsSkeleton()
        self.menuTypesSortView.showSortSkeleton()
        self.menuTypesListView.showMenuTypesSkeleton()
    }

    private func setupBMIView() {
        emptyBMIView.alpha = 1
        emptyBMIView.isHidden = true
        selectionSegmentedControlView.isHidden = true
    }

    private func setupSegmentedControl() {
        selectionSegmentedControl.delegate = self
        let segments = [
            GFSegmentedControl.SegmentItem(title: "Сам подберу",
                                           subtitle: "по калориям и БЖУ",
                                           iconName: "menu_segment"),
            GFSegmentedControl.SegmentItem(title: "Подобрать",
                                           subtitle: "план похудения для меня",
                                           iconName: "imt_segment")
        ]
        selectionSegmentedControl.segments = segments
        selectionSegmentedControl.selectedSegmentIndex = 0
    }

    fileprivate func setupScrollView() {
        scrollView.alwaysBounceVertical = true
        scrollView.delegate = self
        statusBarBackgroundHeight.constant = safeAreaTopHeight
        scrollView.contentInset = .init(top: 16,
                                        left: 0,
                                        bottom: 0,
                                        right: 0)
        if UserService.shared.curUserSettings?.userConfig?.isMenuTypesCatalogWithDescriptionsEnabled == true {
            self.menuTypesSortView.setHidden(true)
            self.menuTypesListView.collectionView.contentInset.top = 12
            self.menuTypesListView.layer.cornerRadius = 32
            self.menuTypesListView.layer.maskedCorners = [.layerMaxXMinYCorner, .layerMinXMinYCorner]
        }
    }

    fileprivate func setupNavigationBar() {
        navigationController?.setNavigationBarHidden(false, animated: false)
        let navigationBar = navigationController?.navigationBar
        navigationBar?.barTintColor = ColorStyle.Black.grey6.value
        navigationBar?.backgroundColor = ColorStyle.Black.grey6.value
        navigationBar?.tintColor = ColorStyle.Black.main.value
        navigationBar?.titleTextAttributes = [.foregroundColor: ColorStyle.Black.main.value]
        let navBarAppearance = UINavigationBarAppearance()
        navBarAppearance.configureWithOpaqueBackground()
        navBarAppearance.backgroundColor = ColorStyle.Black.grey6.value
        navBarAppearance.titleTextAttributes = [.foregroundColor: ColorStyle.Black.main.value]
        navBarAppearance.shadowColor = nil
        navigationBar?.standardAppearance = navBarAppearance
        navigationBar?.scrollEdgeAppearance = navBarAppearance
    }

    @IBAction func openBmi() {
        GFAnalytics.log(.menuSelectionWeightLossBannerClick)
        presenter?.selectedBmi()
    }

    private func setupIMTSummaryView() {
        
        // Создаем IMTSummaryNibView
        imtSummaryView = IMTSummaryNibViewConfigurator.configure(
            menuTypeGroupIdH: "", // Будет установлено позже
            menuTypeCode: "", // Будет установлено позже
            isFirstTime: true,
            delegate: self
        )

        guard let imtSummaryView = imtSummaryView else { return }

//        // Добавляем в иерархию представлений на том же уровне, что и emptyBMIView
//        view.addSubview(imtSummaryView)
//        imtSummaryView.translatesAutoresizingMaskIntoConstraints = false
//
//        // Устанавливаем констрейнты (такие же, как у emptyBMIView)
//        NSLayoutConstraint.activate([
//            imtSummaryView.topAnchor.constraint(equalTo: emptyBMIView.topAnchor),
//            imtSummaryView.leadingAnchor.constraint(equalTo: emptyBMIView.leadingAnchor),
//            imtSummaryView.trailingAnchor.constraint(equalTo: emptyBMIView.trailingAnchor),
//            imtSummaryView.bottomAnchor.constraint(equalTo: emptyBMIView.bottomAnchor)
//        ])

        // Изначально скрываем
        imtSummaryView.isHidden = true
        imtSummaryView.alpha = 0
    }
}

// MARK: - MenuTypesCatalogViewInput: Presenter -> View
extension MenuTypesCatalogViewController: MenuTypesCatalogViewInput {

    func setMenuTypeGroups(_ groups: [MenuTypesGroupViewModel]) {
        Async.main {
            self.menuTypesListView.sections = groups
            let menuTypes = groups.flatMap({ $0.menuTypes })
            if menuTypes.isEmpty {
                //self.loadData()
            }
            else {
                self.menuTypesListView.hideMenuTypesSkeleton()
            }
        }
    }

    func setMenuSectionsConfigViewModel(_ viewModel: MenuSectionsConfigViewModel,
                                        sortModel: MenuSectionCardDropdownViewModel) {
        Async.main {
            self.menuTypesSortView.setHidden(viewModel.sectionDropDowns.count == 1)
            if viewModel.bmiSection != nil {
                GFAnalytics.log(.menuSelectionWeightLossBannerShow)
            }
            self.hideSkeletons()

            /// Убираем ИМТ баннер
            //self.bmiView.setHidden(viewModel.bmiSection == nil)
            self.bmiView.setHidden(true)
            self.selectionSegmentedControlView.isHidden = false

            self.bannersView.setHidden(true)
            self.cardsView.viewModel = viewModel
            self.menuTypesSortView.setViewModel(sortModel,
                                                allModels: viewModel.sectionDropDowns)
        }
    }

    func hideSkeletons() {
        bannersView.hideBannersSkeleton()
        cardsView.hideCardsSkeleton()
        menuTypesSortView.hideSkeletonView()
    }

    func setSectionsError(_ err: GFError) {
        Async.main {
            self.loadingView.isHidden = false
            self.loadingView.showError(err)
        }
    }
}

// MARK: - UIScrollViewDelegate
extension MenuTypesCatalogViewController: UIScrollViewDelegate {

    func scrollViewDidScroll(_ scrollView: UIScrollView) {
        let contentOffsetHeight = max(safeAreaTopHeight, -scrollView.contentOffset.y)
        statusBarBackgroundHeight.constant = contentOffsetHeight
        let topOriginalPoint = bannersView.isHidden ? 0 : bannersView.frame.height
        let navigationBarHeight: CGFloat = presenter?.familyId == nil ? 0 : (navigationController?.navigationBar.frame.height ?? 0)
        let topStickyPoint = topOriginalPoint - safeAreaTopHeight + navigationBarHeight
        var percent: Double = 0
        if scrollView.contentOffset.y > topStickyPoint {
            let newY = topOriginalPoint + scrollView.contentOffset.y - topStickyPoint
            cardsView.frame.y = newY
            percent = ((newY - topOriginalPoint)/cardsView.bounds.height)
            percent = min(percent, 1)
        }
        else {
            cardsView.frame.y = topOriginalPoint
        }
        cardsView.alpha = 1 - percent
        let scaleValue = 1 - percent * 0.15
        let scaleTransform = CGAffineTransform(scaleX: scaleValue,
                                               y: scaleValue)
        cardsView.titleLabel.transform = scaleTransform
        cardsView.collectionView.transform = scaleTransform
        cardsView.titleSkeletonView.transform = scaleTransform
        cardsView.skeletonView.transform = scaleTransform
        if scrollView.contentSize.height > 0 {
            if bmiView.isHidden {
                var bottomViewHeight: CGFloat = (scrollView.contentSize.height - scrollView.contentOffset.y - scrollView.frame.height)
                bottomViewHeight = max(0, -bottomViewHeight)
                let difference = bottomBackgroundViewHeight.constant - bottomViewHeight
                if abs(difference) < 100 {
                    bottomBackgroundViewHeight.constant = bottomViewHeight
                }
            }
            else {
                bottomBackgroundViewHeight.constant = 0
            }
        }
    }
}

// MARK: LoadingViewDelegate
extension MenuTypesCatalogViewController: LoadingViewDelegate {
    func tappedReload(fromMenu: Bool) {
        loadData()
    }
}

//MARK: MenuTypesListSelectSortViewDelegate
extension MenuTypesCatalogViewController: MenuTypesListSelectSortViewDelegate {

    func didSelectNewSort(newSort: MenuSectionCardDropdownViewModel) {
        GFAnalytics.log(.selectedSorting(type: newSort.title))
        self.presenter?.selectedNewSort(newSort)
    }

    func selectSortTapped() {
//        selectsortView = GFDropDownAlert()
//        selectsortView.position = .topCenter
////        selectsortView.anchorView = menuTypesSortView.conteinerView
//        let actions = menuTypesSortView.allModels.map {
//            GFDropDownAlertAction(style: .checkmark,
//                                  icon: nil,
//                                  title: $0.title,
//                                  action: nil) }
//        let curIndex = menuTypesSortView.allModels.firstIndex(where: { action in
//            action.title == (menuTypesSortView.currentSortModel?.title ?? "")
//        })
//        selectsortView.didSelectAction = { [weak self] newIndex in
//            guard let self else { return }
//            if curIndex != newIndex {
//                if let newSort = self.menuTypesSortView.allModels[safe: newIndex] {
//                    GFAnalytics.log(.selectedSorting(type: newSort.title))
//                    self.presenter?.selectedNewSort(newSort)
//                    self.menuTypesSortView.setViewModel(newSort,
//                                                        allModels: self.menuTypesSortView.allModels)
//                }
//            }
//        }
//        selectsortView.addActions(actions)
//        selectsortView.selectedIndex = curIndex
//        selectsortView.show()
    }
}

//MARK: TariffsCatalogCardsViewDelegate
extension MenuTypesCatalogViewController: TariffsCatalogCardsViewDelegate {
    func didSelectedCardWith(viewModel: MenuSectionCardDropdownViewModel) {
        presenter?.selectedCardWith(cardViewModel: viewModel)
        if viewModel.action == .same {
            self.menuTypesSortView.setViewModel(viewModel,
                                                allModels: self.menuTypesSortView.allModels)
        }
    }
}

//MARK: - MenuTypesListViewDelegate
extension MenuTypesCatalogViewController: MenuTypesListViewDelegate {
    func updateScrollViewContentOffset() {
        self.scrollViewDidScroll(scrollView)
    }

    func infoTappedFor(menuType: MenuTypeCardViewModel,
                       groupId: String) {
        let allMenuTypes = menuTypesListView.sections.flatMap{ $0.menuTypes }
        presenter?.infoTapped(menuType,
                              viewModels: allMenuTypes,
                              inGroupWithId: groupId)
    }

    func selectedMenuType(_ menuType: MenuTypeCardViewModel,
                          inGroupWithId groupId: String) {
        presenter?.selectedMenuType(menuType,
                                    inGroupWithId: groupId)
    }
}

//MARK: - BannersViewDelegate
extension MenuTypesCatalogViewController: TariffsCatalogBannersViewDelegate {
    func updateAfterBurnOffer() {
        self.loadData()
    }
}

// MARK: - GFSegmentedControlDelegate
extension MenuTypesCatalogViewController: GFSegmentedControlDelegate {

    func segmentedControl(_ segmentedControl: GFSegmentedControl, didSelectSegmentAt index: Int) {
        switch index {
        case 0:
            showMainContentWithHorizontalAnimation()
        case 1:
            showBMIContentWithHorizontalAnimation()
        default:
            break
        }
    }

    // MARK: - Horizontal Animation Methods
    private func showMainContentWithHorizontalAnimation() {
        menuTypesListView.alpha = 0
        menuTypesSortView.alpha = 0
        menuTypesListView.isHidden = false
        menuTypesSortView.isHidden = false

        menuTypesListView.transform = CGAffineTransform(translationX: -view.frame.width, y: 0)
        menuTypesSortView.transform = CGAffineTransform(translationX: -view.frame.width, y: 0)

        view.setNeedsLayout()
        view.layoutIfNeeded()

        UIView.animate(withDuration: 0.5,
                       delay: 0,
                       usingSpringWithDamping: 0.8,
                       initialSpringVelocity: 0.5,
                       options: [.curveEaseInOut],
                       animations: {
            self.menuTypesListView.transform = .identity
            self.menuTypesSortView.transform = .identity
            self.menuTypesListView.alpha = 1
            self.menuTypesSortView.alpha = 1

            // Скрываем BMI контент (движение вправо + исчезновение)
            if let imtSummaryView = self.imtSummaryView {
                imtSummaryView.transform = CGAffineTransform(translationX: self.view.frame.width, y: 0)
                imtSummaryView.alpha = 0
            }
            self.emptyBMIView.transform = CGAffineTransform(translationX: self.view.frame.width, y: 0)
            self.emptyBMIView.alpha = 0
        }, completion: { _ in
            // Сбрасываем transform для BMI контента
            if let imtSummaryView = self.imtSummaryView {
                imtSummaryView.transform = .identity
            }
            self.emptyBMIView.transform = .identity
        })
    }

    private func showBMIContentWithHorizontalAnimation() {
        // Показываем imtSummaryView вместо emptyBMIView
        if let imtSummaryView = imtSummaryView {
            imtSummaryView.alpha = 0
            imtSummaryView.isHidden = false
            imtSummaryView.transform = CGAffineTransform(translationX: view.frame.width, y: 0)
        }

        // Также подготавливаем emptyBMIView на случай, если imtSummaryView не готов
        emptyBMIView.alpha = 0
        emptyBMIView.isHidden = false
        emptyBMIView.transform = CGAffineTransform(translationX: view.frame.width, y: 0)

        view.setNeedsLayout()
        view.layoutIfNeeded()

        UIView.animate(withDuration: 0.5,
                       delay: 0,
                       usingSpringWithDamping: 0.8,
                       initialSpringVelocity: 0.5,
                       options: [.curveEaseInOut],
                       animations: {
            // Показываем BMI контент (движение справа к центру + появление)
            if let imtSummaryView = self.imtSummaryView {
                imtSummaryView.transform = .identity
                imtSummaryView.alpha = 1
            } else {
                self.emptyBMIView.transform = .identity
                self.emptyBMIView.alpha = 1
            }

            // Скрываем основной контент (движение влево + исчезновение)
            self.menuTypesListView.transform = CGAffineTransform(translationX: -self.view.frame.width, y: 0)
            self.menuTypesSortView.transform = CGAffineTransform(translationX: -self.view.frame.width, y: 0)
            self.menuTypesListView.alpha = 0
            self.menuTypesSortView.alpha = 0
        }, completion: { _ in
            // Сбрасываем transform для основного контента
            self.menuTypesListView.transform = .identity
            self.menuTypesSortView.transform = .identity
        })
    }
}

// MARK: - GFSegmentedControlDelegate
extension MenuTypesCatalogViewController: EmptyBMIViewDelegate {
    func startBMISettings() {
        //TODO: - отображаем ИМТ
        //self.presenter?.selectedBmi()

        //Открываем заполнение ИМТ
        self.presenter?.openBmi()
    }
}

// MARK: - IMTSummaryNibViewDelegate
extension MenuTypesCatalogViewController: IMTSummaryNibViewDelegate {
    func openMenuType(menuType: MenuTypeBmiViewModel,
                      firstMealDate: Date?,
                      firstMealSession: String?,
                      sectionId: String) {
        // Делегируем вызов презентеру
        //presenter?.openMenuType(menuType: menuType,
        //                       firstMealDate: firstMealDate,
        //                       firstMealSession: firstMealSession,
        //                       sectionId: sectionId)
    }

    func openInfoBmi(viewModel: MenuTypeBmiViewModel) {
        // Делегируем вызов презентеру
        //presenter?.openInfoBmi(viewModel: viewModel)
    }

    func openEditWeight() {
        // Делегируем вызов презентеру
        //presenter?.openEditWeight()
    }

    func openFaq() {
        // Делегируем вызов презентеру
        //presenter?.openFaq()
    }

    func openNormCalories() {
        // Делегируем вызов презентеру
        //presenter?.openNormCalories()
    }

    func openEditBMI() {
        // Делегируем вызов презентеру
        //presenter?.openEditBMI()
    }

    func openAllFilters(delegate: MenuFilterSettingsModuleDelegate) {
        // Делегируем вызов презентеру
        //presenter?.openAllFilters(delegate: delegate)
    }
}
