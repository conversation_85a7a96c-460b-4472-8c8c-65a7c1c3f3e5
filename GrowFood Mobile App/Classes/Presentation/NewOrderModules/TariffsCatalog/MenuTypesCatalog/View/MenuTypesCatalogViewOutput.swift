//
//  MenuTypesCatalogViewOutput.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 18/10/2022.
//  Copyright © 2022 GrowFood. All rights reserved.
//

protocol MenuTypesCatalogViewOutput: AnyNewOrderModule, Lifecycable {
    func selectedNewSort(_ sort: MenuSectionCardDropdownViewModel)
    func selectedCardWith(cardViewModel: MenuSectionCardDropdownViewModel)
    func selectedMenuType(_ menuType: MenuTypeCardViewModel,
                          inGroupWithId groupId: String)
    func infoTapped(_ menuType: MenuTypeCardViewModel,
                    viewModels: [MenuTypeCardViewModel],
                    inGroupWithId groupId: String)
    func selectedBmi()
    func openBmi()
    var familyId: String? { get }
    
    /// IMTSummaryNibViewDelegate
    func openMenuType(menuType: MenuTypeBmiViewModel,
                      firstMealDate: Date?,
                      firstMealSession: String?,
                      sectionId: String)
    func openInfoBmi(viewModel: MenuTypeBmiViewModel)
    func openEditWeight()
    func openNormCalories()
    func openEditBMI()
    func openAllFilters(delegate: MenuFilterSettingsModuleDelegate)
}
