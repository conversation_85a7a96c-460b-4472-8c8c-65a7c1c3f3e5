//
//  MenuTypesCatalogPresenter.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 18/10/2022.
//  Copyright © 2022 GrowFood. All rights reserved.
//
import FirebasePerformance

final class MenuTypesCatalogPresenter: MenuTypesCatalogPresenterInput {

    // MARK: VIPER Stack Properties

    weak var view: MenuTypesCatalogViewInput!
    var interactor: MenuTypesCatalogInteractorInput!
    var router: MenuTypesCatalogRouterInput!

    weak var delegate: MenuTypesCatalogModuleDelegate?
    var familyId: String? = nil
    
    var viewModel: MenuSectionsConfigViewModel? = nil
    var curGroups: [MenuTypesGroupViewModel] = []
    var curSortModel: MenuSectionCardDropdownViewModel? = nil {
        didSet {
            guard let curSortModel else { return }
            interactor.loadMenuTypes(forDropdownId: curSortModel.id)
        }
    }
    var linkForOpen: String? = nil

    var defaultStartDate: Date? = nil
    var defaultStartSession: DeliveryDay.DeliveryType? = nil
    
    private var menuTypeCurrent: MenuTypeCardViewModel?
    private var inGroupWithIdCurrent: String?
    
    init (defaultStartDate: Date? = nil,
          defaultStartSession: DeliveryDay.DeliveryType?) {
        self.defaultStartDate = defaultStartDate
        self.defaultStartSession = defaultStartSession
    }
    
    
    // MARK: - MenuTypesCatalogPresenterInput
    func viewDidLoad() {
        if curGroups.isEmpty {
            PerformanceTracker.startTrace(with: .catalogLoading)
        }
        interactor.loadSections()
    }

}

// MARK: - MenuTypesCatalogViewOutput: View -> Presenter
extension MenuTypesCatalogPresenter: MenuTypesCatalogViewOutput {
    func selectedBmi() {
        guard let bmiModel = interactor.bmiMotivation() else { return }
        router.openBmi(bmiViewModel: bmiModel,
                       cardViewModel: viewModel?.bmiSection ?? MenuSectionCardDropdownViewModel(),
                       delegate: self)
    }
    
    func openBmi() {
        let cardViewModel = viewModel?.bmiSection ?? MenuSectionCardDropdownViewModel()
        openBmiSettings(catalogId: cardViewModel.id)
    }
    
    func selectedMenuType(_ menuType: MenuTypeCardViewModel,
                          inGroupWithId groupId: String) {
        /// Если необходима миграция на новые фильтры
        if UserService.shared.isProposeFiltersChange {
            menuTypeCurrent = menuType
            inGroupWithIdCurrent = groupId
            router.openAllFilters(delegate: self)
        }
        else {
            selectedMenuType(menuType,
                             inGroupWithId: groupId,
                             from: "sorting")
        }
    }
    
    func infoTapped(_ menuType: MenuTypeCardViewModel,
                    viewModels: [MenuTypeCardViewModel],
                    inGroupWithId groupId: String) {
        router.infoOpen(menuType,
                        viewModels: viewModels,
                        inGroupWithId: groupId,
                        delegate: self)
    }
    
    func selectedCardWith(cardViewModel: MenuSectionCardDropdownViewModel) {
        let isViewPresented = (view as? UIViewController)?.navigationController?.presentingViewController != nil
        switch cardViewModel.action {
        case .deeplink:
            if let url = cardViewModel.link {
                GFURLRouter.shared.openURL(url)
            }
        case .same:
            self.curSortModel = cardViewModel
        case .bmi:
            if let bmiViewModel = interactor.bmiMotivation(),
                familyId == nil, isViewPresented == false {
                router.openBmi(bmiViewModel: bmiViewModel,
                               cardViewModel: cardViewModel,
                               delegate: self)
            }
            else {
                router.openMenuTypesWith(cardViewModel: cardViewModel,
                                         delegate: self)
            }
        case .popup:
            router.openMenuTypesWith(cardViewModel: cardViewModel,
                                     delegate: self)
        }
    }
    
    func selectedNewSort(_ sort: MenuSectionCardDropdownViewModel) {
        self.curSortModel = sort
    }
    
    func openLink(_ link: String) {
        if self.viewModel == nil {
            self.linkForOpen = link
        }
        else {
            self.goToLink(link: link)
        }
    }
    
    func goToLink(link: String) {
        defer {
            self.linkForOpen = nil
        }
        if let sectionDeeplinkCode = link.split(separator: "/").first {
            guard let viewModel, let section = viewModel.getSectionBy(deeplinkCode: String(sectionDeeplinkCode)) else {
                return
            }
            let tariffCode: String = String(link.split(separator: "/").last ?? "")
            if tariffCode == "" {
                self.selectedCardWith(cardViewModel: section)
            }
            else {
                if let menuType = interactor.menuTypeByCode(tariffCode,
                                                            sectionId: section.id) {
                    /// Если необходима миграция на новые фильтры
                    if UserService.shared.isProposeFiltersChange {
                        menuTypeCurrent = menuType
                        inGroupWithIdCurrent = section.id
                        router.openAllFilters(delegate: self)
                    }
                    else {
                        self.router.openMenuType(menuType: menuType,
                                                 menuCatalogSectionId: section.id,
                                                 familyId: self.familyId,
                                                 defaultStartDate: self.defaultStartDate,
                                                 defaultStartSession: self.defaultStartSession,
                                                 menuTypesDelegate: delegate,
                                                 fromLink: true)
                    }
                }
                else {
                    self.selectedCardWith(cardViewModel: section)
                }
            }
        }
    }
    
    func setForceApplyFilter(isForce: Bool) {
    }
    
    func popToRootViewController(animated: Bool) {
        router.popToRootViewController(animated: animated)
    }
    
    func openMenuType(menuType: MenuTypeBmiViewModel,
                      firstMealDate: Date?,
                      firstMealSession: String?,
                      sectionId: String) {
        router?.openMenuType(menuType: menuType,
                             firstMealDate: firstMealDate,
                             firstMealSession: firstMealSession,
                             sectionId: sectionId)
    }
    
    func openEditBMI() {
        router.openEditBMI()
    }
    
    func openNormCalories() {
        router.openNormCalories()
    }
    
    func openEditWeight() {
        router.openEditWeight()
    }
    
    func openInfoBmi(viewModel: MenuTypeBmiViewModel) {
        router.openInfoBmi(viewModel: viewModel)
    }
    
    func openAllFilters(delegate: MenuFilterSettingsModuleDelegate) {
        router.openAllFilters(delegate: self)
    }
}

// MARK: MenuTypesCatalogInteractorOutput: Interactor -> Presenter
extension MenuTypesCatalogPresenter: MenuTypesCatalogInteractorOutput {
    
    func setMenuTypeGroups(_ groups: [MenuTypesGroupViewModel]) {
        self.curGroups = groups
        view.setMenuTypeGroups(groups)
        PerformanceTracker.stopTrace(with: .catalogLoading)
    }
    
    func setMenuSectionsConfigViewModel(_ viewModel: MenuSectionsConfigViewModel) {
        self.viewModel = viewModel
        if self.curSortModel == nil {
            self.curSortModel = viewModel.sectionDropDowns.first(where: { model in
                model.isDefault == true
            })
        }
        else {
            self.curSortModel = viewModel.sectionDropDowns.first(where: { model in
                model.id == self.curSortModel?.id
            })
        }
        if self.curSortModel == nil {
            self.curSortModel = viewModel.sectionDropDowns.first
        }
        guard let curSortModel else {
            view.setSectionsError(GFError(title: "Ошибка обработки данных"))
            return
        }
        view.setMenuSectionsConfigViewModel(viewModel,
                                            sortModel: curSortModel)
        if let linkForOpen {
            Async.main {
                self.goToLink(link: linkForOpen)
            }
        }
    }
    
    func setSectionsError(_ err: GFError) {
        view.setSectionsError(err)
    }
}

//MARK: MenuTypesForCardModuleDelegate
extension MenuTypesCatalogPresenter: MenuTypesForCardModuleDelegate {
    func selectedMenuType(_ menuType: MenuTypeCardViewModel,
                          inGroupWithId groupId: String,
                          from: String) {
        if let section = viewModel?.getSectionBy(id: groupId) {
            GFAnalytics.log(.selectedMenuType(type: menuType.menuType,
                                              sorting: section.sortField,
                                              from: from))
        }
        
        /// Если необходима миграция на новые фильтры
        if UserService.shared.isProposeFiltersChange {
            menuTypeCurrent = menuType
            inGroupWithIdCurrent = groupId
            router.openAllFilters(delegate: self)
        }
        else {
            self.router.openMenuType(menuType: menuType,
                                     menuCatalogSectionId: groupId,
                                     familyId: familyId,
                                     defaultStartDate: defaultStartDate,
                                     defaultStartSession: defaultStartSession, 
                                     menuTypesDelegate: delegate,
                                     fromLink: false)
        }
    }
}

//MARK: BMIMotiovationMenuTypesModuleDelegate
extension MenuTypesCatalogPresenter: BMIMotiovationMenuTypesModuleDelegate {
    func openBmiCards(cardViewModel: MenuSectionCardDropdownViewModel) {
        router.openMenuTypesWith(cardViewModel: cardViewModel,
                                 delegate: self)
    }
    
    func openBmiSettings(catalogId: String) {
        let goalModel = GoalViewModel(bmiViewModel: interactor.bmiMotivation(),
                                      catalogId: catalogId)
        router.openBmiSettings(model: goalModel)
    }
}

//MARK: MenuTypeInfoModuleDelegate
extension MenuTypesCatalogPresenter: MenuTypeInfoModuleDelegate {
    func selectedMenuTypeFromInfo(_ menuType: MenuTypeCardViewModel,
                                  inGroupWithId groupId: String) {
        selectedMenuType(menuType,
                         inGroupWithId: groupId,
                         from: "info")
    }
}

//MARK: MenuFilterSettingsModuleDelegate
extension MenuTypesCatalogPresenter: MenuFilterSettingsModuleDelegate {
    func completePop() {
        guard let menuTypeCurrent = self.menuTypeCurrent,
              let inGroupWithIdCurrent = self.inGroupWithIdCurrent else { return }
        self.selectedMenuType(menuTypeCurrent,
                              inGroupWithId: inGroupWithIdCurrent,
                              from: "sorting")
    }
}
