//
//  IMTSummaryGoalItemCollectionViewCell.swift
//  GrowFood Mobile App
//
//  Created by <PERSON> on 04.03.2021.
//  Copyright © 2021 GrowFood. All rights reserved.
//

import UIKit

protocol IMTSummaryGoalItemCollectionViewCellDelegate {
    func selectedAt(indexPath: IndexPath)
    func tappedInfoAt(indexPath: IndexPath)
}

final class IMTSummaryGoalItemCollectionViewCell: BaseCardCollectionViewCell {

    @IBOutlet weak var curType: UIImageView!
    @IBOutlet weak var topSpace: NSLayoutConstraint!
    @IBOutlet weak var bottomConstraint: NSLayoutConstraint!
    @IBOutlet weak var botDescriptionLabel: GFLabelB2Regular!
    @IBOutlet weak var infoButton: UIButton!
    @IBOutlet weak var selectButton: GFButtonB2Medium!
    @IBOutlet weak var backView: UIView!
    @IBOutlet var rightIcons: [UIImageView]!
    @IBOutlet weak var descriptionLabel: GFLabelB2Regular!
    @IBOutlet weak var titleLabel: GFLabelH1!
    @IBOutlet weak var kcalLabel: GFLabelB3Regular!
    @IBOutlet weak var priceLabel: GFLabelB3Regular!
    @IBOutlet weak var mealsLabel: GFLabelB3Regular!
    var delegate: IMTSummaryGoalItemCollectionViewCellDelegate? = nil
    private var indexPathLocal: IndexPath?
    
    override func awakeFromNib() {
        super.awakeFromNib()
        // Initiкalization code
        rightIcons.forEach { (iconView) in
            iconView.image = iconView.image?.withRenderingMode(.alwaysTemplate)
        }
        self.animatableView = backView
        self.backView.layer.applySketchShadow(color: .black,
                                              alpha: 0.1,
                                              x: 0,
                                              y: 4,
                                              blur: 16,
                                              spread: 0)
    }

    func setGroup(_ group: IMTSummaryGroupViewModel,
                  at indexPath: IndexPath,
                  forceSelectButton: Bool = false) {
        indexPathLocal = indexPath
        topSpace.constant = 14
        curType.isHidden = true
        let color = UIColor(hex: group.textColor)
        rightIcons.forEach { (iconView) in
            iconView.tintColor = color
        }
        if let menuTypeModel = group.menuTypeBmiModels[safe: indexPath.item-1] {
            infoButton.isHidden = true//menuTypeModel.marketingInfo == nil
            priceLabel.text = "от \(menuTypeModel.baseDayPrice.moneyRoundString)/день"
            mealsLabel.text = menuTypeModel.numberOfMeals
            kcalLabel.text = menuTypeModel.calories
            
            if let purposeWeightInfo = menuTypeModel.purposeWeightInfo {
                titleLabel.text = purposeWeightInfo.title
                descriptionLabel.text = "Программа \(menuTypeModel.tariffTitle)"
                botDescriptionLabel.text = menuTypeModel.bmiText
            }
            else {
                botDescriptionLabel.text = ""
                titleLabel.text = menuTypeModel.tariffTitle
                descriptionLabel.text = menuTypeModel.tariffDescr
            }
            if indexPath == IndexPath(item: 1, section: 1) || forceSelectButton {
                selectButton.isHidden = false
            }
            else {
                selectButton.isHidden = true
            }
            var botSpace: CGFloat = 8
            if indexPath.item == group.menuTypeBmiModels.count {
                botSpace = 24
            }
            bottomConstraint.constant = botSpace
        }
    }
    
    func setGroup(_ group: IMTSummaryGroupViewModel,
                  viewModel: MenuTypeBmiViewModel) {
        topSpace.constant = 20
        curType.isHidden = false
        let color = UIColor(hex: group.textColor)
        rightIcons.forEach { (iconView) in
            iconView.tintColor = color
        }
        infoButton.isHidden = viewModel.marketingInfo == nil
        priceLabel.text = "от \(viewModel.baseDayPrice.moneyRoundString)/день"
        mealsLabel.text = viewModel.meals
        kcalLabel.text = viewModel.calories
        
        if let purposeWeightInfo = viewModel.purposeWeightInfo {
            titleLabel.text = purposeWeightInfo.title
            descriptionLabel.text = "Программа \(viewModel.tariffTitle)"
            botDescriptionLabel.text = viewModel.bmiText
        }
        else {
            botDescriptionLabel.text = ""
            titleLabel.text = viewModel.tariffTitle
            descriptionLabel.text = viewModel.tariffDescr
        }
        if indexPath == IndexPath(item: 1, section: 1) {
            selectButton.isHidden = false
        }
        else {
            selectButton.isHidden = true
        }
        let botSpace: CGFloat = 24
        
        bottomConstraint.constant = botSpace
    }

    
    @IBAction func selectButtonTapped() {
        if let ip = indexPath {
            delegate?.selectedAt(indexPath: ip)
        }
        else if let indexPathLocal {
            delegate?.selectedAt(indexPath: indexPathLocal)
        }
    }
    
    @IBAction func tappedInfo() {
        if let ip = indexPath {
            delegate?.tappedInfoAt(indexPath: ip)
        }
        else if let indexPathLocal {
            delegate?.tappedInfoAt(indexPath: indexPathLocal)
        }
    }
    
}


